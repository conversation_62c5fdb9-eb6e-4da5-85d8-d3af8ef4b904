{"version": 3, "file": "convert.js", "sources": ["../../../src/js/convert.ts"], "sourcesContent": ["/**\n * convert.js\n */\n\nimport { LRUCache } from 'lru-cache';\nimport {\n  convertColorToHsl,\n  convertColorToHwb,\n  convertColorToLab,\n  convertColorToLch,\n  convertColorToOklab,\n  convertColorToOklch,\n  convertColorToRgb,\n  numberToHexString,\n  parseColorFunc,\n  parseColorValue\n} from './color';\nimport { isString } from './common';\nimport { cssCalc } from './css-calc';\nimport { cssVar } from './css-var';\nimport { resolveRelativeColor } from './relative-color';\nimport { resolve } from './resolve';\nimport { valueToJsonString } from './util';\n\n/* constants */\nimport {\n  SYN_FN_MATH_CALC,\n  SYN_FN_REL,\n  SYN_FN_VAR,\n  VAL_COMP\n} from './constant.js';\n\n/* regexp */\nconst REG_FN_MATH_CALC = new RegExp(SYN_FN_MATH_CALC);\nconst REG_FN_REL = new RegExp(SYN_FN_REL);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/* cached results */\nexport const cachedResults = new LRUCache({\n  max: 4096\n});\n\n/**\n * pre process\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {?string} - value\n */\nexport const preProcess = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    dimension?: object;\n    format?: string;\n  } = {}\n): string | null => {\n  if (isString(value)) {\n    value = value.trim();\n    if (!value) {\n      return null;\n    }\n  } else {\n    return null;\n  }\n  const { customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{preProcess:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as string | null;\n    }\n  }\n  if (REG_FN_VAR.test(value)) {\n    const resolvedValue = cssVar(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue as string;\n    } else {\n      if (cacheKey) {\n        cachedResults.set(cacheKey, resolvedValue!);\n      }\n      return null;\n    }\n  }\n  if (REG_FN_REL.test(value)) {\n    value = resolveRelativeColor(value, opt) as string;\n  } else if (REG_FN_MATH_CALC.test(value)) {\n    const resolvedValue = cssCalc(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue as string;\n    } else {\n      if (cacheKey) {\n        cachedResults.set(cacheKey, resolvedValue!);\n      }\n      return null;\n    }\n  }\n  if (value.startsWith('color-mix')) {\n    value = resolve(value, {\n      format: VAL_COMP\n    }) as string;\n  }\n  if (cacheKey) {\n    cachedResults.set(cacheKey, value);\n  }\n  return value;\n};\n\n/**\n * convert number to hex string\n * @param {number} value - color value\n * @returns {string} - hex string: 00..ff\n */\nexport const numberToHex = (value: number): string => {\n  const cacheKey = typeof value === 'number' && `{numberToHex:${value}}`;\n  if (cacheKey && cachedResults.has(cacheKey)) {\n    return cachedResults.get(cacheKey) as string;\n  }\n  const hex = numberToHexString(value);\n  if (cacheKey) {\n    cachedResults.set(cacheKey, hex);\n  }\n  return hex;\n};\n\n/**\n * convert color to hex\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {boolean} [opt.alpha] - return in #rrggbbaa notation\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {?string} - #rrggbb | #rrggbbaa | null\n */\nexport const colorToHex = (\n  value: string,\n  opt: {\n    alpha?: boolean;\n    customProperty?: object;\n    dimension?: object;\n    format?: string;\n  } = {}\n): string | null => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue.toLowerCase() as string;\n    } else {\n      return null!;\n    }\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { alpha, customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{colorToHex:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as string | null;\n    }\n  }\n  let hex;\n  if (alpha) {\n    opt.format = 'hexAlpha';\n    hex = resolve(value, opt) as string | null;\n  } else {\n    opt.format = 'hex';\n    hex = resolve(value, opt) as string | null;\n  }\n  if (cacheKey) {\n    cachedResults.set(cacheKey, hex!);\n  }\n  return hex;\n};\n\n/**\n * convert color to hsl\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {Array.<number>} - [h, s, l, alpha]\n */\nexport const colorToHsl = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    dimension?: object;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue.toLowerCase() as string;\n    } else {\n      return [0, 0, 0, 0];\n    }\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{colorToHsl:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as Array<number>;\n    }\n  }\n  opt.format = 'hsl';\n  const hsl = convertColorToHsl(value, opt) as Array<number>;\n  if (cacheKey) {\n    cachedResults.set(cacheKey, hsl);\n  }\n  return hsl;\n};\n\n/**\n * convert color to hwb\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {Array.<number>} - [h, w, b, alpha]\n */\nexport const colorToHwb = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    dimension?: object;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue.toLowerCase() as string;\n    } else {\n      return [0, 0, 0, 0];\n    }\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{colorToHwb:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as Array<number>;\n    }\n  }\n  opt.format = 'hwb';\n  const hwb = convertColorToHwb(value, opt) as Array<number>;\n  if (cacheKey) {\n    cachedResults.set(cacheKey, hwb);\n  }\n  return hwb;\n};\n\n/**\n * convert color to lab\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {Array.<number>} - [l, a, b, alpha]\n */\nexport const colorToLab = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    dimension?: object;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue.toLowerCase() as string;\n    } else {\n      return [0, 0, 0, 0];\n    }\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{colorToLab:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as Array<number>;\n    }\n  }\n  const lab = convertColorToLab(value, opt) as Array<number>;\n  if (cacheKey) {\n    cachedResults.set(cacheKey, lab);\n  }\n  return lab;\n};\n\n/**\n * convert color to lch\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {Array.<number>} - [l, c, h, alpha]\n */\nexport const colorToLch = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    dimension?: object;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue.toLowerCase() as string;\n    } else {\n      return [0, 0, 0, 0];\n    }\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{colorToLch:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as Array<number>;\n    }\n  }\n  const lch = convertColorToLch(value, opt) as Array<number>;\n  if (cacheKey) {\n    cachedResults.set(cacheKey, lch);\n  }\n  return lch;\n};\n\n/**\n * convert color to oklab\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {Array.<number>} - [l, a, b, alpha]\n */\nexport const colorToOklab = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    dimension?: object;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue.toLowerCase() as string;\n    } else {\n      return [0, 0, 0, 0];\n    }\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{colorToOklab:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as Array<number>;\n    }\n  }\n  const lab = convertColorToOklab(value, opt) as Array<number>;\n  if (cacheKey) {\n    cachedResults.set(cacheKey, lab);\n  }\n  return lab;\n};\n\n/**\n * convert color to oklch\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {Array.<number>} - [l, c, h, alpha]\n */\nexport const colorToOklch = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    dimension?: object;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue.toLowerCase() as string;\n    } else {\n      return [0, 0, 0, 0];\n    }\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{colorToOklch:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as Array<number>;\n    }\n  }\n  const lch = convertColorToOklch(value, opt) as Array<number>;\n  if (cacheKey) {\n    cachedResults.set(cacheKey, lch);\n  }\n  return lch;\n};\n\n/**\n * convert color to rgb\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {Array.<number>} - [r, g, b, alpha]\n */\nexport const colorToRgb = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    dimension?: object;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue.toLowerCase() as string;\n    } else {\n      return [0, 0, 0, 0];\n    }\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{colorToRgb:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as Array<number>;\n    }\n  }\n  const rgb = convertColorToRgb(value, opt) as Array<number>;\n  if (cacheKey) {\n    cachedResults.set(cacheKey, rgb);\n  }\n  return rgb;\n};\n\n/**\n * convert color to xyz\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.d50] - white poin in d50\n * @param {object} [opt.dimension] - dimension\n * @returns {Array.<number>} - [x, y, z, alpha]\n */\nexport const colorToXyz = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    d50?: boolean;\n    dimension?: object;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt) as string | null;\n    if (resolvedValue) {\n      value = resolvedValue.toLowerCase() as string;\n    } else {\n      return [0, 0, 0, 0];\n    }\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { customProperty } = opt;\n  let cacheKey;\n  if (\n    typeof (\n      customProperty as { callback?: (item: string) => string }\n    )?.callback !== 'function'\n  ) {\n    cacheKey = `{colorToXyz:${value},opt:${valueToJsonString(opt)}}`;\n    if (cachedResults.has(cacheKey)) {\n      return cachedResults.get(cacheKey) as Array<number>;\n    }\n  }\n  let xyz;\n  if (value.startsWith('color(')) {\n    [, ...xyz] = parseColorFunc(value, opt) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  } else {\n    [, ...xyz] = parseColorValue(value, opt) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n  if (cacheKey) {\n    cachedResults.set(cacheKey, xyz as Array<number>);\n  }\n  return xyz as Array<number>;\n};\n\n/**\n * convert color to xyz-d50\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {object} [opt.customProperty] - custom properties\n * @param {object} [opt.dimension] - dimension\n * @returns {Array.<number>} - [x, y, z, alpha]\n */\nexport const colorToXyzD50 = (\n  value: string,\n  opt: {\n    customProperty?: object;\n    d50?: boolean;\n    dimension?: object;\n    format?: string;\n  } = {}\n): Array<number> => {\n  opt.d50 = true;\n  return colorToXyz(value, opt) as Array<number>;\n};\n\n/* convert */\nexport const convert = {\n  colorToHex,\n  colorToHsl,\n  colorToHwb,\n  colorToLab,\n  colorToLch,\n  colorToOklab,\n  colorToOklch,\n  colorToRgb,\n  colorToXyz,\n  colorToXyzD50,\n  numberToHex\n};\n"], "names": [], "mappings": ";;;;;;;;;AAiCA,MAAM,mBAAmB,IAAI,OAAO,gBAAgB;AACpD,MAAM,aAAa,IAAI,OAAO,UAAU;AACxC,MAAM,aAAa,IAAI,OAAO,UAAU;AAG3B,MAAA,gBAAgB,IAAI,SAAS;AAAA,EACxC,KAAK;AACP,CAAC;AAUM,MAAM,aAAa,CACxB,OACA,MAII,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACnB,QAAI,CAAC,OAAO;AACH,aAAA;AAAA,IAAA;AAAA,EACT,OACK;AACE,WAAA;AAAA,EAAA;AAEH,QAAA,EAAE,mBAAmB;AACvB,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,eAAe,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AACzD,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEE,MAAA,WAAW,KAAK,KAAK,GAAG;AACpB,UAAA,gBAAgB,OAAO,OAAO,GAAG;AACvC,QAAI,eAAe;AACT,cAAA;AAAA,IAAA,OACH;AACL,UAAI,UAAU;AACE,sBAAA,IAAI,UAAU,aAAc;AAAA,MAAA;AAErC,aAAA;AAAA,IAAA;AAAA,EACT;AAEE,MAAA,WAAW,KAAK,KAAK,GAAG;AAClB,YAAA,qBAAqB,OAAO,GAAG;AAAA,EAC9B,WAAA,iBAAiB,KAAK,KAAK,GAAG;AACjC,UAAA,gBAAgB,QAAQ,OAAO,GAAG;AACxC,QAAI,eAAe;AACT,cAAA;AAAA,IAAA,OACH;AACL,UAAI,UAAU;AACE,sBAAA,IAAI,UAAU,aAAc;AAAA,MAAA;AAErC,aAAA;AAAA,IAAA;AAAA,EACT;AAEE,MAAA,MAAM,WAAW,WAAW,GAAG;AACjC,YAAQ,QAAQ,OAAO;AAAA,MACrB,QAAQ;AAAA,IAAA,CACT;AAAA,EAAA;AAEH,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,KAAK;AAAA,EAAA;AAE5B,SAAA;AACT;AAOa,MAAA,cAAc,CAAC,UAA0B;AACpD,QAAM,WAAW,OAAO,UAAU,YAAY,gBAAgB,KAAK;AACnE,MAAI,YAAY,cAAc,IAAI,QAAQ,GAAG;AACpC,WAAA,cAAc,IAAI,QAAQ;AAAA,EAAA;AAE7B,QAAA,MAAM,kBAAkB,KAAK;AACnC,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAG;AAAA,EAAA;AAE1B,SAAA;AACT;AAWO,MAAM,aAAa,CACxB,OACA,MAKI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,eAAe;AACjB,cAAQ,cAAc,YAAY;AAAA,IAAA,OAC7B;AACE,aAAA;AAAA,IAAA;AAAA,EACT,OACK;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,OAAO,eAAA,IAAmB;AAC9B,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,eAAe,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AACzD,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEE,MAAA;AACJ,MAAI,OAAO;AACT,QAAI,SAAS;AACP,UAAA,QAAQ,OAAO,GAAG;AAAA,EAAA,OACnB;AACL,QAAI,SAAS;AACP,UAAA,QAAQ,OAAO,GAAG;AAAA,EAAA;AAE1B,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAI;AAAA,EAAA;AAE3B,SAAA;AACT;AAUO,MAAM,aAAa,CACxB,OACA,MAII,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,eAAe;AACjB,cAAQ,cAAc,YAAY;AAAA,IAAA,OAC7B;AACL,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EACpB,OACK;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,mBAAmB;AACvB,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,eAAe,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AACzD,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEF,MAAI,SAAS;AACP,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAG;AAAA,EAAA;AAE1B,SAAA;AACT;AAUO,MAAM,aAAa,CACxB,OACA,MAII,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,eAAe;AACjB,cAAQ,cAAc,YAAY;AAAA,IAAA,OAC7B;AACL,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EACpB,OACK;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,mBAAmB;AACvB,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,eAAe,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AACzD,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEF,MAAI,SAAS;AACP,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAG;AAAA,EAAA;AAE1B,SAAA;AACT;AAUO,MAAM,aAAa,CACxB,OACA,MAII,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,eAAe;AACjB,cAAQ,cAAc,YAAY;AAAA,IAAA,OAC7B;AACL,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EACpB,OACK;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,mBAAmB;AACvB,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,eAAe,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AACzD,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEI,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAG;AAAA,EAAA;AAE1B,SAAA;AACT;AAUO,MAAM,aAAa,CACxB,OACA,MAII,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,eAAe;AACjB,cAAQ,cAAc,YAAY;AAAA,IAAA,OAC7B;AACL,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EACpB,OACK;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,mBAAmB;AACvB,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,eAAe,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AACzD,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEI,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAG;AAAA,EAAA;AAE1B,SAAA;AACT;AAUO,MAAM,eAAe,CAC1B,OACA,MAII,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,eAAe;AACjB,cAAQ,cAAc,YAAY;AAAA,IAAA,OAC7B;AACL,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EACpB,OACK;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,mBAAmB;AACvB,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,iBAAiB,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AAC3D,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEI,QAAA,MAAM,oBAAoB,OAAO,GAAG;AAC1C,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAG;AAAA,EAAA;AAE1B,SAAA;AACT;AAUO,MAAM,eAAe,CAC1B,OACA,MAII,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,eAAe;AACjB,cAAQ,cAAc,YAAY;AAAA,IAAA,OAC7B;AACL,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EACpB,OACK;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,mBAAmB;AACvB,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,iBAAiB,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AAC3D,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEI,QAAA,MAAM,oBAAoB,OAAO,GAAG;AAC1C,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAG;AAAA,EAAA;AAE1B,SAAA;AACT;AAUO,MAAM,aAAa,CACxB,OACA,MAII,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,eAAe;AACjB,cAAQ,cAAc,YAAY;AAAA,IAAA,OAC7B;AACL,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EACpB,OACK;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,mBAAmB;AACvB,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,eAAe,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AACzD,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEI,QAAA,MAAM,kBAAkB,OAAO,GAAG;AACxC,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAG;AAAA,EAAA;AAE1B,SAAA;AACT;AAWO,MAAM,aAAa,CACxB,OACA,MAKI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACb,UAAA,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,eAAe;AACjB,cAAQ,cAAc,YAAY;AAAA,IAAA,OAC7B;AACL,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EACpB,OACK;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,mBAAmB;AACvB,MAAA;AAEF,MAAA,QACE,iDACC,cAAa,YAChB;AACA,eAAW,eAAe,KAAK,QAAQ,kBAAkB,GAAG,CAAC;AACzD,QAAA,cAAc,IAAI,QAAQ,GAAG;AACxB,aAAA,cAAc,IAAI,QAAQ;AAAA,IAAA;AAAA,EACnC;AAEE,MAAA;AACA,MAAA,MAAM,WAAW,QAAQ,GAAG;AAC9B,KAAA,EAAG,GAAG,GAAG,IAAI,eAAe,OAAO,GAAG;AAAA,EAAA,OAOjC;AACL,KAAA,EAAG,GAAG,GAAG,IAAI,gBAAgB,OAAO,GAAG;AAAA,EAAA;AAQzC,MAAI,UAAU;AACE,kBAAA,IAAI,UAAU,GAAoB;AAAA,EAAA;AAE3C,SAAA;AACT;AAUO,MAAM,gBAAgB,CAC3B,OACA,MAKI,OACc;AAClB,MAAI,MAAM;AACH,SAAA,WAAW,OAAO,GAAG;AAC9B;AAGO,MAAM,UAAU;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;"}