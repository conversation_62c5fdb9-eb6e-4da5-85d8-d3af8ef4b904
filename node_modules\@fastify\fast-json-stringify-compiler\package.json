{"name": "@fastify/fast-json-stringify-compiler", "description": "Build and manage the fast-json-stringify instances for the fastify framework", "version": "5.0.2", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "unit": "tap test/**/*.test.js", "test": "npm run unit && npm run test:typescript", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fast-json-stringify-compiler.git"}, "keywords": [], "author": "<PERSON> <<EMAIL>> (https://github.com/Eomm)", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fast-json-stringify-compiler/issues"}, "homepage": "https://github.com/fastify/fast-json-stringify-compiler#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "eslint": "^9.17.0", "fastify": "^5.0.0", "neostandard": "^0.12.0", "sanitize-filename": "^1.6.3", "tap": "^18.7.2", "tsd": "^0.31.0"}, "pre-commit": ["lint", "test"], "dependencies": {"fast-json-stringify": "^6.0.0"}}