var li=Object.defineProperty,ma=t=>{throw TypeError(t)},ui=(t,e,n)=>e in t?li(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,j=(t,e,n)=>ui(t,typeof e!="symbol"?e+"":e,n),as=(t,e,n)=>e.has(t)||ma("Cannot "+n),h=(t,e,n)=>(as(t,e,"read from private field"),n?n.call(t):e.get(t)),L=(t,e,n)=>e.has(t)?ma("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),k=(t,e,n,r)=>(as(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),B=(t,e,n)=>(as(t,e,"access private method"),n),os=(t,e,n,r)=>({set _(s){k(t,e,s,n)},get _(){return h(t,e,r)}});const hn=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,va=new Set,is=typeof process=="object"&&process?process:{},ba=(t,e,n,r)=>{typeof is.emitWarning=="function"?is.emitWarning(t,e,n,r):console.error(`[${n}] ${e}: ${t}`)};let wr=globalThis.AbortController,wa=globalThis.AbortSignal;var ya;if(typeof wr>"u"){wa=class{constructor(){j(this,"onabort"),j(this,"_onabort",[]),j(this,"reason"),j(this,"aborted",!1)}addEventListener(n,r){this._onabort.push(r)}},wr=class{constructor(){j(this,"signal",new wa),e()}abort(n){var r,s;if(!this.signal.aborted){this.signal.reason=n,this.signal.aborted=!0;for(const a of this.signal._onabort)a(n);(s=(r=this.signal).onabort)==null||s.call(r,n)}}};let t=((ya=is.env)==null?void 0:ya.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const e=()=>{t&&(t=!1,ba("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}const ci=t=>!va.has(t),Le=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),$a=t=>Le(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?yr:null:null;class yr extends Array{constructor(e){super(e),this.fill(0)}}var Un;const Na=class br{constructor(e,n){if(j(this,"heap"),j(this,"length"),!h(br,Un))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new n(e),this.length=0}static create(e){const n=$a(e);if(!n)return[];k(br,Un,!0);const r=new br(e,n);return k(br,Un,!1),r}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}};Un=new WeakMap,L(Na,Un,!1);let hi=Na;var Ea,Ca,fe,zt,pe,de,Gn,jn,vt,Ut,dt,st,q,Pt,Gt,xt,bt,ge,wt,me,ve,jt,be,Ke,Dt,M,ls,fn,Ie,$r,qt,ka,pn,qn,Nr,_e,He,us,Er,Cr,nt,cs,Vn,ze,hs;const fi=class ii{constructor(e){L(this,M),L(this,fe),L(this,zt),L(this,pe),L(this,de),L(this,Gn),L(this,jn),j(this,"ttl"),j(this,"ttlResolution"),j(this,"ttlAutopurge"),j(this,"updateAgeOnGet"),j(this,"updateAgeOnHas"),j(this,"allowStale"),j(this,"noDisposeOnSet"),j(this,"noUpdateTTL"),j(this,"maxEntrySize"),j(this,"sizeCalculation"),j(this,"noDeleteOnFetchRejection"),j(this,"noDeleteOnStaleGet"),j(this,"allowStaleOnFetchAbort"),j(this,"allowStaleOnFetchRejection"),j(this,"ignoreFetchAbort"),L(this,vt),L(this,Ut),L(this,dt),L(this,st),L(this,q),L(this,Pt),L(this,Gt),L(this,xt),L(this,bt),L(this,ge),L(this,wt),L(this,me),L(this,ve),L(this,jt),L(this,be),L(this,Ke),L(this,Dt),L(this,fn,()=>{}),L(this,Ie,()=>{}),L(this,$r,()=>{}),L(this,qt,()=>!1),L(this,pn,I=>{}),L(this,qn,(I,G,Y)=>{}),L(this,Nr,(I,G,Y,kt)=>{if(Y||kt)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0}),j(this,Ea,"LRUCache");const{max:n=0,ttl:r,ttlResolution:s=1,ttlAutopurge:a,updateAgeOnGet:o,updateAgeOnHas:i,allowStale:l,dispose:u,disposeAfter:c,noDisposeOnSet:f,noUpdateTTL:m,maxSize:d=0,maxEntrySize:$=0,sizeCalculation:N,fetchMethod:b,memoMethod:p,noDeleteOnFetchRejection:y,noDeleteOnStaleGet:E,allowStaleOnFetchRejection:C,allowStaleOnFetchAbort:S,ignoreFetchAbort:O}=e;if(n!==0&&!Le(n))throw new TypeError("max option must be a nonnegative integer");const D=n?$a(n):Array;if(!D)throw new Error("invalid max value: "+n);if(k(this,fe,n),k(this,zt,d),this.maxEntrySize=$||h(this,zt),this.sizeCalculation=N,this.sizeCalculation){if(!h(this,zt)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(p!==void 0&&typeof p!="function")throw new TypeError("memoMethod must be a function if defined");if(k(this,jn,p),b!==void 0&&typeof b!="function")throw new TypeError("fetchMethod must be a function if specified");if(k(this,Gn,b),k(this,Ke,!!b),k(this,dt,new Map),k(this,st,new Array(n).fill(void 0)),k(this,q,new Array(n).fill(void 0)),k(this,Pt,new D(n)),k(this,Gt,new D(n)),k(this,xt,0),k(this,bt,0),k(this,ge,hi.create(n)),k(this,vt,0),k(this,Ut,0),typeof u=="function"&&k(this,pe,u),typeof c=="function"?(k(this,de,c),k(this,wt,[])):(k(this,de,void 0),k(this,wt,void 0)),k(this,be,!!h(this,pe)),k(this,Dt,!!h(this,de)),this.noDisposeOnSet=!!f,this.noUpdateTTL=!!m,this.noDeleteOnFetchRejection=!!y,this.allowStaleOnFetchRejection=!!C,this.allowStaleOnFetchAbort=!!S,this.ignoreFetchAbort=!!O,this.maxEntrySize!==0){if(h(this,zt)!==0&&!Le(h(this,zt)))throw new TypeError("maxSize must be a positive integer if specified");if(!Le(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");B(this,M,ka).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!E,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!i,this.ttlResolution=Le(s)||s===0?s:1,this.ttlAutopurge=!!a,this.ttl=r||0,this.ttl){if(!Le(this.ttl))throw new TypeError("ttl must be a positive integer if specified");B(this,M,ls).call(this)}if(h(this,fe)===0&&this.ttl===0&&h(this,zt)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!h(this,fe)&&!h(this,zt)){const I="LRU_CACHE_UNBOUNDED";ci(I)&&(va.add(I),ba("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",I,ii))}}static unsafeExposeInternals(e){return{starts:h(e,ve),ttls:h(e,jt),sizes:h(e,me),keyMap:h(e,dt),keyList:h(e,st),valList:h(e,q),next:h(e,Pt),prev:h(e,Gt),get head(){return h(e,xt)},get tail(){return h(e,bt)},free:h(e,ge),isBackgroundFetch:n=>{var r;return B(r=e,M,nt).call(r,n)},backgroundFetch:(n,r,s,a)=>{var o;return B(o=e,M,Cr).call(o,n,r,s,a)},moveToTail:n=>{var r;return B(r=e,M,Vn).call(r,n)},indexes:n=>{var r;return B(r=e,M,_e).call(r,n)},rindexes:n=>{var r;return B(r=e,M,He).call(r,n)},isStale:n=>{var r;return h(r=e,qt).call(r,n)}}}get max(){return h(this,fe)}get maxSize(){return h(this,zt)}get calculatedSize(){return h(this,Ut)}get size(){return h(this,vt)}get fetchMethod(){return h(this,Gn)}get memoMethod(){return h(this,jn)}get dispose(){return h(this,pe)}get disposeAfter(){return h(this,de)}getRemainingTTL(e){return h(this,dt).has(e)?1/0:0}*entries(){for(const e of B(this,M,_e).call(this))h(this,q)[e]!==void 0&&h(this,st)[e]!==void 0&&!B(this,M,nt).call(this,h(this,q)[e])&&(yield[h(this,st)[e],h(this,q)[e]])}*rentries(){for(const e of B(this,M,He).call(this))h(this,q)[e]!==void 0&&h(this,st)[e]!==void 0&&!B(this,M,nt).call(this,h(this,q)[e])&&(yield[h(this,st)[e],h(this,q)[e]])}*keys(){for(const e of B(this,M,_e).call(this)){const n=h(this,st)[e];n!==void 0&&!B(this,M,nt).call(this,h(this,q)[e])&&(yield n)}}*rkeys(){for(const e of B(this,M,He).call(this)){const n=h(this,st)[e];n!==void 0&&!B(this,M,nt).call(this,h(this,q)[e])&&(yield n)}}*values(){for(const e of B(this,M,_e).call(this))h(this,q)[e]!==void 0&&!B(this,M,nt).call(this,h(this,q)[e])&&(yield h(this,q)[e])}*rvalues(){for(const e of B(this,M,He).call(this))h(this,q)[e]!==void 0&&!B(this,M,nt).call(this,h(this,q)[e])&&(yield h(this,q)[e])}[(Ca=Symbol.iterator,Ea=Symbol.toStringTag,Ca)](){return this.entries()}find(e,n={}){for(const r of B(this,M,_e).call(this)){const s=h(this,q)[r],a=B(this,M,nt).call(this,s)?s.__staleWhileFetching:s;if(a!==void 0&&e(a,h(this,st)[r],this))return this.get(h(this,st)[r],n)}}forEach(e,n=this){for(const r of B(this,M,_e).call(this)){const s=h(this,q)[r],a=B(this,M,nt).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&e.call(n,a,h(this,st)[r],this)}}rforEach(e,n=this){for(const r of B(this,M,He).call(this)){const s=h(this,q)[r],a=B(this,M,nt).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&e.call(n,a,h(this,st)[r],this)}}purgeStale(){let e=!1;for(const n of B(this,M,He).call(this,{allowStale:!0}))h(this,qt).call(this,n)&&(B(this,M,ze).call(this,h(this,st)[n],"expire"),e=!0);return e}info(e){const n=h(this,dt).get(e);if(n===void 0)return;const r=h(this,q)[n],s=B(this,M,nt).call(this,r)?r.__staleWhileFetching:r;if(s===void 0)return;const a={value:s};if(h(this,jt)&&h(this,ve)){const o=h(this,jt)[n],i=h(this,ve)[n];if(o&&i){const l=o-(hn.now()-i);a.ttl=l,a.start=Date.now()}}return h(this,me)&&(a.size=h(this,me)[n]),a}dump(){const e=[];for(const n of B(this,M,_e).call(this,{allowStale:!0})){const r=h(this,st)[n],s=h(this,q)[n],a=B(this,M,nt).call(this,s)?s.__staleWhileFetching:s;if(a===void 0||r===void 0)continue;const o={value:a};if(h(this,jt)&&h(this,ve)){o.ttl=h(this,jt)[n];const i=hn.now()-h(this,ve)[n];o.start=Math.floor(Date.now()-i)}h(this,me)&&(o.size=h(this,me)[n]),e.unshift([r,o])}return e}load(e){this.clear();for(const[n,r]of e){if(r.start){const s=Date.now()-r.start;r.start=hn.now()-s}this.set(n,r.value,r)}}set(e,n,r={}){var s,a,o,i,l;if(n===void 0)return this.delete(e),this;const{ttl:u=this.ttl,start:c,noDisposeOnSet:f=this.noDisposeOnSet,sizeCalculation:m=this.sizeCalculation,status:d}=r;let{noUpdateTTL:$=this.noUpdateTTL}=r;const N=h(this,Nr).call(this,e,n,r.size||0,m);if(this.maxEntrySize&&N>this.maxEntrySize)return d&&(d.set="miss",d.maxEntrySizeExceeded=!0),B(this,M,ze).call(this,e,"set"),this;let b=h(this,vt)===0?void 0:h(this,dt).get(e);if(b===void 0)b=h(this,vt)===0?h(this,bt):h(this,ge).length!==0?h(this,ge).pop():h(this,vt)===h(this,fe)?B(this,M,Er).call(this,!1):h(this,vt),h(this,st)[b]=e,h(this,q)[b]=n,h(this,dt).set(e,b),h(this,Pt)[h(this,bt)]=b,h(this,Gt)[b]=h(this,bt),k(this,bt,b),os(this,vt)._++,h(this,qn).call(this,b,N,d),d&&(d.set="add"),$=!1;else{B(this,M,Vn).call(this,b);const p=h(this,q)[b];if(n!==p){if(h(this,Ke)&&B(this,M,nt).call(this,p)){p.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:y}=p;y!==void 0&&!f&&(h(this,be)&&((s=h(this,pe))==null||s.call(this,y,e,"set")),h(this,Dt)&&((a=h(this,wt))==null||a.push([y,e,"set"])))}else f||(h(this,be)&&((o=h(this,pe))==null||o.call(this,p,e,"set")),h(this,Dt)&&((i=h(this,wt))==null||i.push([p,e,"set"])));if(h(this,pn).call(this,b),h(this,qn).call(this,b,N,d),h(this,q)[b]=n,d){d.set="replace";const y=p&&B(this,M,nt).call(this,p)?p.__staleWhileFetching:p;y!==void 0&&(d.oldValue=y)}}else d&&(d.set="update")}if(u!==0&&!h(this,jt)&&B(this,M,ls).call(this),h(this,jt)&&($||h(this,$r).call(this,b,u,c),d&&h(this,Ie).call(this,d,b)),!f&&h(this,Dt)&&h(this,wt)){const p=h(this,wt);let y;for(;y=p?.shift();)(l=h(this,de))==null||l.call(this,...y)}return this}pop(){var e;try{for(;h(this,vt);){const n=h(this,q)[h(this,xt)];if(B(this,M,Er).call(this,!0),B(this,M,nt).call(this,n)){if(n.__staleWhileFetching)return n.__staleWhileFetching}else if(n!==void 0)return n}}finally{if(h(this,Dt)&&h(this,wt)){const n=h(this,wt);let r;for(;r=n?.shift();)(e=h(this,de))==null||e.call(this,...r)}}}has(e,n={}){const{updateAgeOnHas:r=this.updateAgeOnHas,status:s}=n,a=h(this,dt).get(e);if(a!==void 0){const o=h(this,q)[a];if(B(this,M,nt).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(h(this,qt).call(this,a))s&&(s.has="stale",h(this,Ie).call(this,s,a));else return r&&h(this,fn).call(this,a),s&&(s.has="hit",h(this,Ie).call(this,s,a)),!0}else s&&(s.has="miss");return!1}peek(e,n={}){const{allowStale:r=this.allowStale}=n,s=h(this,dt).get(e);if(s===void 0||!r&&h(this,qt).call(this,s))return;const a=h(this,q)[s];return B(this,M,nt).call(this,a)?a.__staleWhileFetching:a}async fetch(e,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:i=this.noDisposeOnSet,size:l=0,sizeCalculation:u=this.sizeCalculation,noUpdateTTL:c=this.noUpdateTTL,noDeleteOnFetchRejection:f=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:m=this.allowStaleOnFetchRejection,ignoreFetchAbort:d=this.ignoreFetchAbort,allowStaleOnFetchAbort:$=this.allowStaleOnFetchAbort,context:N,forceRefresh:b=!1,status:p,signal:y}=n;if(!h(this,Ke))return p&&(p.fetch="get"),this.get(e,{allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,status:p});const E={allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,ttl:o,noDisposeOnSet:i,size:l,sizeCalculation:u,noUpdateTTL:c,noDeleteOnFetchRejection:f,allowStaleOnFetchRejection:m,allowStaleOnFetchAbort:$,ignoreFetchAbort:d,status:p,signal:y};let C=h(this,dt).get(e);if(C===void 0){p&&(p.fetch="miss");const S=B(this,M,Cr).call(this,e,C,E,N);return S.__returned=S}else{const S=h(this,q)[C];if(B(this,M,nt).call(this,S)){const G=r&&S.__staleWhileFetching!==void 0;return p&&(p.fetch="inflight",G&&(p.returnedStale=!0)),G?S.__staleWhileFetching:S.__returned=S}const O=h(this,qt).call(this,C);if(!b&&!O)return p&&(p.fetch="hit"),B(this,M,Vn).call(this,C),s&&h(this,fn).call(this,C),p&&h(this,Ie).call(this,p,C),S;const D=B(this,M,Cr).call(this,e,C,E,N),I=D.__staleWhileFetching!==void 0&&r;return p&&(p.fetch=O?"stale":"refresh",I&&O&&(p.returnedStale=!0)),I?D.__staleWhileFetching:D.__returned=D}}async forceFetch(e,n={}){const r=await this.fetch(e,n);if(r===void 0)throw new Error("fetch() returned undefined");return r}memo(e,n={}){const r=h(this,jn);if(!r)throw new Error("no memoMethod provided to constructor");const{context:s,forceRefresh:a,...o}=n,i=this.get(e,o);if(!a&&i!==void 0)return i;const l=r(e,i,{options:o,context:s});return this.set(e,l,o),l}get(e,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,status:o}=n,i=h(this,dt).get(e);if(i!==void 0){const l=h(this,q)[i],u=B(this,M,nt).call(this,l);return o&&h(this,Ie).call(this,o,i),h(this,qt).call(this,i)?(o&&(o.get="stale"),u?(o&&r&&l.__staleWhileFetching!==void 0&&(o.returnedStale=!0),r?l.__staleWhileFetching:void 0):(a||B(this,M,ze).call(this,e,"expire"),o&&r&&(o.returnedStale=!0),r?l:void 0)):(o&&(o.get="hit"),u?l.__staleWhileFetching:(B(this,M,Vn).call(this,i),s&&h(this,fn).call(this,i),l))}else o&&(o.get="miss")}delete(e){return B(this,M,ze).call(this,e,"delete")}clear(){return B(this,M,hs).call(this,"delete")}};fe=new WeakMap,zt=new WeakMap,pe=new WeakMap,de=new WeakMap,Gn=new WeakMap,jn=new WeakMap,vt=new WeakMap,Ut=new WeakMap,dt=new WeakMap,st=new WeakMap,q=new WeakMap,Pt=new WeakMap,Gt=new WeakMap,xt=new WeakMap,bt=new WeakMap,ge=new WeakMap,wt=new WeakMap,me=new WeakMap,ve=new WeakMap,jt=new WeakMap,be=new WeakMap,Ke=new WeakMap,Dt=new WeakMap,M=new WeakSet,ls=function(){const t=new yr(h(this,fe)),e=new yr(h(this,fe));k(this,jt,t),k(this,ve,e),k(this,$r,(s,a,o=hn.now())=>{if(e[s]=a!==0?o:0,t[s]=a,a!==0&&this.ttlAutopurge){const i=setTimeout(()=>{h(this,qt).call(this,s)&&B(this,M,ze).call(this,h(this,st)[s],"expire")},a+1);i.unref&&i.unref()}}),k(this,fn,s=>{e[s]=t[s]!==0?hn.now():0}),k(this,Ie,(s,a)=>{if(t[a]){const o=t[a],i=e[a];if(!o||!i)return;s.ttl=o,s.start=i,s.now=n||r();const l=s.now-i;s.remainingTTL=o-l}});let n=0;const r=()=>{const s=hn.now();if(this.ttlResolution>0){n=s;const a=setTimeout(()=>n=0,this.ttlResolution);a.unref&&a.unref()}return s};this.getRemainingTTL=s=>{const a=h(this,dt).get(s);if(a===void 0)return 0;const o=t[a],i=e[a];if(!o||!i)return 1/0;const l=(n||r())-i;return o-l},k(this,qt,s=>{const a=e[s],o=t[s];return!!o&&!!a&&(n||r())-a>o})},fn=new WeakMap,Ie=new WeakMap,$r=new WeakMap,qt=new WeakMap,ka=function(){const t=new yr(h(this,fe));k(this,Ut,0),k(this,me,t),k(this,pn,e=>{k(this,Ut,h(this,Ut)-t[e]),t[e]=0}),k(this,Nr,(e,n,r,s)=>{if(B(this,M,nt).call(this,n))return 0;if(!Le(r))if(s){if(typeof s!="function")throw new TypeError("sizeCalculation must be a function");if(r=s(n,e),!Le(r))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return r}),k(this,qn,(e,n,r)=>{if(t[e]=n,h(this,zt)){const s=h(this,zt)-t[e];for(;h(this,Ut)>s;)B(this,M,Er).call(this,!0)}k(this,Ut,h(this,Ut)+t[e]),r&&(r.entrySize=n,r.totalCalculatedSize=h(this,Ut))})},pn=new WeakMap,qn=new WeakMap,Nr=new WeakMap,_e=function*({allowStale:t=this.allowStale}={}){if(h(this,vt))for(let e=h(this,bt);!(!B(this,M,us).call(this,e)||((t||!h(this,qt).call(this,e))&&(yield e),e===h(this,xt)));)e=h(this,Gt)[e]},He=function*({allowStale:t=this.allowStale}={}){if(h(this,vt))for(let e=h(this,xt);!(!B(this,M,us).call(this,e)||((t||!h(this,qt).call(this,e))&&(yield e),e===h(this,bt)));)e=h(this,Pt)[e]},us=function(t){return t!==void 0&&h(this,dt).get(h(this,st)[t])===t},Er=function(t){var e,n;const r=h(this,xt),s=h(this,st)[r],a=h(this,q)[r];return h(this,Ke)&&B(this,M,nt).call(this,a)?a.__abortController.abort(new Error("evicted")):(h(this,be)||h(this,Dt))&&(h(this,be)&&((e=h(this,pe))==null||e.call(this,a,s,"evict")),h(this,Dt)&&((n=h(this,wt))==null||n.push([a,s,"evict"]))),h(this,pn).call(this,r),t&&(h(this,st)[r]=void 0,h(this,q)[r]=void 0,h(this,ge).push(r)),h(this,vt)===1?(k(this,xt,k(this,bt,0)),h(this,ge).length=0):k(this,xt,h(this,Pt)[r]),h(this,dt).delete(s),os(this,vt)._--,r},Cr=function(t,e,n,r){const s=e===void 0?void 0:h(this,q)[e];if(B(this,M,nt).call(this,s))return s;const a=new wr,{signal:o}=n;o?.addEventListener("abort",()=>a.abort(o.reason),{signal:a.signal});const i={signal:a.signal,options:n,context:r},l=($,N=!1)=>{const{aborted:b}=a.signal,p=n.ignoreFetchAbort&&$!==void 0;if(n.status&&(b&&!N?(n.status.fetchAborted=!0,n.status.fetchError=a.signal.reason,p&&(n.status.fetchAbortIgnored=!0)):n.status.fetchResolved=!0),b&&!p&&!N)return c(a.signal.reason);const y=m;return h(this,q)[e]===m&&($===void 0?y.__staleWhileFetching?h(this,q)[e]=y.__staleWhileFetching:B(this,M,ze).call(this,t,"fetch"):(n.status&&(n.status.fetchUpdated=!0),this.set(t,$,i.options))),$},u=$=>(n.status&&(n.status.fetchRejected=!0,n.status.fetchError=$),c($)),c=$=>{const{aborted:N}=a.signal,b=N&&n.allowStaleOnFetchAbort,p=b||n.allowStaleOnFetchRejection,y=p||n.noDeleteOnFetchRejection,E=m;if(h(this,q)[e]===m&&(!y||E.__staleWhileFetching===void 0?B(this,M,ze).call(this,t,"fetch"):b||(h(this,q)[e]=E.__staleWhileFetching)),p)return n.status&&E.__staleWhileFetching!==void 0&&(n.status.returnedStale=!0),E.__staleWhileFetching;if(E.__returned===E)throw $},f=($,N)=>{var b;const p=(b=h(this,Gn))==null?void 0:b.call(this,t,s,i);p&&p instanceof Promise&&p.then(y=>$(y===void 0?void 0:y),N),a.signal.addEventListener("abort",()=>{(!n.ignoreFetchAbort||n.allowStaleOnFetchAbort)&&($(void 0),n.allowStaleOnFetchAbort&&($=y=>l(y,!0)))})};n.status&&(n.status.fetchDispatched=!0);const m=new Promise(f).then(l,u),d=Object.assign(m,{__abortController:a,__staleWhileFetching:s,__returned:void 0});return e===void 0?(this.set(t,d,{...i.options,status:void 0}),e=h(this,dt).get(t)):h(this,q)[e]=d,d},nt=function(t){if(!h(this,Ke))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof wr},cs=function(t,e){h(this,Gt)[e]=t,h(this,Pt)[t]=e},Vn=function(t){t!==h(this,bt)&&(t===h(this,xt)?k(this,xt,h(this,Pt)[t]):B(this,M,cs).call(this,h(this,Gt)[t],h(this,Pt)[t]),B(this,M,cs).call(this,h(this,bt),t),k(this,bt,t))},ze=function(t,e){var n,r,s,a;let o=!1;if(h(this,vt)!==0){const i=h(this,dt).get(t);if(i!==void 0)if(o=!0,h(this,vt)===1)B(this,M,hs).call(this,e);else{h(this,pn).call(this,i);const l=h(this,q)[i];if(B(this,M,nt).call(this,l)?l.__abortController.abort(new Error("deleted")):(h(this,be)||h(this,Dt))&&(h(this,be)&&((n=h(this,pe))==null||n.call(this,l,t,e)),h(this,Dt)&&((r=h(this,wt))==null||r.push([l,t,e]))),h(this,dt).delete(t),h(this,st)[i]=void 0,h(this,q)[i]=void 0,i===h(this,bt))k(this,bt,h(this,Gt)[i]);else if(i===h(this,xt))k(this,xt,h(this,Pt)[i]);else{const u=h(this,Gt)[i];h(this,Pt)[u]=h(this,Pt)[i];const c=h(this,Pt)[i];h(this,Gt)[c]=h(this,Gt)[i]}os(this,vt)._--,h(this,ge).push(i)}}if(h(this,Dt)&&(s=h(this,wt))!=null&&s.length){const i=h(this,wt);let l;for(;l=i?.shift();)(a=h(this,de))==null||a.call(this,...l)}return o},hs=function(t){var e,n,r;for(const s of B(this,M,He).call(this,{allowStale:!0})){const a=h(this,q)[s];if(B(this,M,nt).call(this,a))a.__abortController.abort(new Error("deleted"));else{const o=h(this,st)[s];h(this,be)&&((e=h(this,pe))==null||e.call(this,a,o,t)),h(this,Dt)&&((n=h(this,wt))==null||n.push([a,o,t]))}}if(h(this,dt).clear(),h(this,q).fill(void 0),h(this,st).fill(void 0),h(this,jt)&&h(this,ve)&&(h(this,jt).fill(0),h(this,ve).fill(0)),h(this,me)&&h(this,me).fill(0),k(this,xt,0),k(this,bt,0),h(this,ge).length=0,k(this,Ut,0),k(this,vt,0),h(this,Dt)&&h(this,wt)){const s=h(this,wt);let a;for(;a=s?.shift();)(r=h(this,de))==null||r.call(this,...a)}};let Xn=fi;const V=t=>typeof t=="string"||t instanceof String;class Kn extends Error{constructor(e,n,r,s){super(e),j(this,"sourceStart"),j(this,"sourceEnd"),j(this,"parserState"),this.name="ParseError",this.sourceStart=n,this.sourceEnd=r,this.parserState=s}}class Ue extends Kn{constructor(e,n,r,s,a){super(e,n,r,s),j(this,"token"),this.token=a}}const xe={UnexpectedNewLineInString:"Unexpected newline while consuming a string token.",UnexpectedEOFInString:"Unexpected EOF while consuming a string token.",UnexpectedEOFInComment:"Unexpected EOF while consuming a comment.",UnexpectedEOFInURL:"Unexpected EOF while consuming a url token.",UnexpectedEOFInEscapedCodePoint:"Unexpected EOF while consuming an escaped code point.",UnexpectedCharacterInURL:"Unexpected character while consuming a url token.",InvalidEscapeSequenceInURL:"Invalid escape sequence while consuming a url token.",InvalidEscapeSequenceAfterBackslash:'Invalid escape sequence after "\\"'};function oe(...t){let e="";for(let n=0;n<t.length;n++)e+=t[n][1];return e}const dn=13,yt=45,gn=10,mn=43,vn=65533;function pi(t){return t.source.codePointAt(t.cursor)===60&&t.source.codePointAt(t.cursor+1)===33&&t.source.codePointAt(t.cursor+2)===yt&&t.source.codePointAt(t.cursor+3)===yt}function Vt(t){return t>=48&&t<=57}function di(t){return t>=65&&t<=90}function gi(t){return t>=97&&t<=122}function Ze(t){return t>=48&&t<=57||t>=97&&t<=102||t>=65&&t<=70}function mi(t){return gi(t)||di(t)}function bn(t){return mi(t)||vi(t)||t===95}function fs(t){return bn(t)||Vt(t)||t===yt}function vi(t){return t===183||t===8204||t===8205||t===8255||t===8256||t===8204||192<=t&&t<=214||216<=t&&t<=246||248<=t&&t<=893||895<=t&&t<=8191||8304<=t&&t<=8591||11264<=t&&t<=12271||12289<=t&&t<=55295||63744<=t&&t<=64975||65008<=t&&t<=65533||t===0||!!wn(t)||t>=65536}function kr(t){return t===gn||t===dn||t===12}function Ye(t){return t===32||t===gn||t===9||t===dn||t===12}function wn(t){return t>=55296&&t<=57343}function yn(t){return t.source.codePointAt(t.cursor)===92&&!kr(t.source.codePointAt(t.cursor+1)??-1)}function Fr(t,e){return e.source.codePointAt(e.cursor)===yt?e.source.codePointAt(e.cursor+1)===yt||!!bn(e.source.codePointAt(e.cursor+1)??-1)||e.source.codePointAt(e.cursor+1)===92&&!kr(e.source.codePointAt(e.cursor+2)??-1):!!bn(e.source.codePointAt(e.cursor)??-1)||yn(e)}function Fa(t){return t.source.codePointAt(t.cursor)===mn||t.source.codePointAt(t.cursor)===yt?!!Vt(t.source.codePointAt(t.cursor+1)??-1)||t.source.codePointAt(t.cursor+1)===46&&Vt(t.source.codePointAt(t.cursor+2)??-1):t.source.codePointAt(t.cursor)===46?Vt(t.source.codePointAt(t.cursor+1)??-1):Vt(t.source.codePointAt(t.cursor)??-1)}function bi(t){return t.source.codePointAt(t.cursor)===47&&t.source.codePointAt(t.cursor+1)===42}function wi(t){return t.source.codePointAt(t.cursor)===yt&&t.source.codePointAt(t.cursor+1)===yt&&t.source.codePointAt(t.cursor+2)===62}var v,F,Sr;function yi(t){switch(t){case v.OpenParen:return v.CloseParen;case v.CloseParen:return v.OpenParen;case v.OpenCurly:return v.CloseCurly;case v.CloseCurly:return v.OpenCurly;case v.OpenSquare:return v.CloseSquare;case v.CloseSquare:return v.OpenSquare;default:return null}}function $i(t){switch(t[0]){case v.OpenParen:return[v.CloseParen,")",-1,-1,void 0];case v.CloseParen:return[v.OpenParen,"(",-1,-1,void 0];case v.OpenCurly:return[v.CloseCurly,"}",-1,-1,void 0];case v.CloseCurly:return[v.OpenCurly,"{",-1,-1,void 0];case v.OpenSquare:return[v.CloseSquare,"]",-1,-1,void 0];case v.CloseSquare:return[v.OpenSquare,"[",-1,-1,void 0];default:return null}}function Ni(t,e){for(e.advanceCodePoint(2);;){const n=e.readCodePoint();if(n===void 0){const r=[v.Comment,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new Ue(xe.UnexpectedEOFInComment,e.representationStart,e.representationEnd,["4.3.2. Consume comments","Unexpected EOF"],r)),r}if(n===42&&e.source.codePointAt(e.cursor)!==void 0&&e.source.codePointAt(e.cursor)===47){e.advanceCodePoint();break}}return[v.Comment,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0]}function xr(t,e){const n=e.readCodePoint();if(n===void 0)return t.onParseError(new Kn(xe.UnexpectedEOFInEscapedCodePoint,e.representationStart,e.representationEnd,["4.3.7. Consume an escaped code point","Unexpected EOF"])),vn;if(Ze(n)){const r=[n];let s;for(;(s=e.source.codePointAt(e.cursor))!==void 0&&Ze(s)&&r.length<6;)r.push(s),e.advanceCodePoint();Ye(e.source.codePointAt(e.cursor)??-1)&&(e.source.codePointAt(e.cursor)===dn&&e.source.codePointAt(e.cursor+1)===gn&&e.advanceCodePoint(),e.advanceCodePoint());const a=parseInt(String.fromCodePoint(...r),16);return a===0||wn(a)||a>1114111?vn:a}return n===0||wn(n)?vn:n}function Ar(t,e){const n=[];for(;;){const r=e.source.codePointAt(e.cursor)??-1;if(r===0||wn(r))n.push(vn),e.advanceCodePoint(+(r>65535)+1);else if(fs(r))n.push(r),e.advanceCodePoint(+(r>65535)+1);else{if(!yn(e))return n;e.advanceCodePoint(),n.push(xr(t,e))}}}function Ei(t,e){e.advanceCodePoint();const n=e.source.codePointAt(e.cursor);if(n!==void 0&&(fs(n)||yn(e))){let r=Sr.Unrestricted;Fr(0,e)&&(r=Sr.ID);const s=Ar(t,e);return[v.Hash,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...s),type:r}]}return[v.Delim,"#",e.representationStart,e.representationEnd,{value:"#"}]}function Ci(t,e){let n=F.Integer;for(e.source.codePointAt(e.cursor)!==mn&&e.source.codePointAt(e.cursor)!==yt||e.advanceCodePoint();Vt(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===46&&Vt(e.source.codePointAt(e.cursor+1)??-1))for(e.advanceCodePoint(2),n=F.Number;Vt(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===101||e.source.codePointAt(e.cursor)===69){if(Vt(e.source.codePointAt(e.cursor+1)??-1))e.advanceCodePoint(2);else{if(e.source.codePointAt(e.cursor+1)!==yt&&e.source.codePointAt(e.cursor+1)!==mn||!Vt(e.source.codePointAt(e.cursor+2)??-1))return n;e.advanceCodePoint(3)}for(n=F.Number;Vt(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint()}return n}function ps(t,e){let n;{const a=e.source.codePointAt(e.cursor);a===yt?n="-":a===mn&&(n="+")}const r=Ci(0,e),s=parseFloat(e.source.slice(e.representationStart,e.representationEnd+1));if(Fr(0,e)){const a=Ar(t,e);return[v.Dimension,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n,type:r,unit:String.fromCodePoint(...a)}]}return e.source.codePointAt(e.cursor)===37?(e.advanceCodePoint(),[v.Percentage,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n}]):[v.Number,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n,type:r}]}function ki(t){for(;Ye(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();return[v.Whitespace,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0]}(function(t){t.Comment="comment",t.AtKeyword="at-keyword-token",t.BadString="bad-string-token",t.BadURL="bad-url-token",t.CDC="CDC-token",t.CDO="CDO-token",t.Colon="colon-token",t.Comma="comma-token",t.Delim="delim-token",t.Dimension="dimension-token",t.EOF="EOF-token",t.Function="function-token",t.Hash="hash-token",t.Ident="ident-token",t.Number="number-token",t.Percentage="percentage-token",t.Semicolon="semicolon-token",t.String="string-token",t.URL="url-token",t.Whitespace="whitespace-token",t.OpenParen="(-token",t.CloseParen=")-token",t.OpenSquare="[-token",t.CloseSquare="]-token",t.OpenCurly="{-token",t.CloseCurly="}-token",t.UnicodeRange="unicode-range-token"})(v||(v={})),function(t){t.Integer="integer",t.Number="number"}(F||(F={})),function(t){t.Unrestricted="unrestricted",t.ID="id"}(Sr||(Sr={}));class Fi{constructor(e){j(this,"cursor",0),j(this,"source",""),j(this,"representationStart",0),j(this,"representationEnd",-1),this.source=e}advanceCodePoint(e=1){this.cursor=this.cursor+e,this.representationEnd=this.cursor-1}readCodePoint(){const e=this.source.codePointAt(this.cursor);if(e!==void 0)return this.cursor=this.cursor+1,this.representationEnd=this.cursor-1,e}unreadCodePoint(e=1){this.cursor=this.cursor-e,this.representationEnd=this.cursor-1}resetRepresentation(){this.representationStart=this.cursor,this.representationEnd=-1}}function Si(t,e){let n="";const r=e.readCodePoint();for(;;){const s=e.readCodePoint();if(s===void 0){const a=[v.String,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new Ue(xe.UnexpectedEOFInString,e.representationStart,e.representationEnd,["4.3.5. Consume a string token","Unexpected EOF"],a)),a}if(kr(s)){e.unreadCodePoint();const a=[v.BadString,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new Ue(xe.UnexpectedNewLineInString,e.representationStart,e.source.codePointAt(e.cursor)===dn&&e.source.codePointAt(e.cursor+1)===gn?e.representationEnd+2:e.representationEnd+1,["4.3.5. Consume a string token","Unexpected newline"],a)),a}if(s===r)return[v.String,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];if(s!==92)s===0||wn(s)?n+=String.fromCodePoint(vn):n+=String.fromCodePoint(s);else{if(e.source.codePointAt(e.cursor)===void 0)continue;if(kr(e.source.codePointAt(e.cursor)??-1)){e.source.codePointAt(e.cursor)===dn&&e.source.codePointAt(e.cursor+1)===gn&&e.advanceCodePoint(),e.advanceCodePoint();continue}n+=String.fromCodePoint(xr(t,e))}}}function xi(t){return!(t.length!==3||t[0]!==117&&t[0]!==85||t[1]!==114&&t[1]!==82||t[2]!==108&&t[2]!==76)}function ds(t,e){for(;;){const n=e.source.codePointAt(e.cursor);if(n===void 0)return;if(n===41)return void e.advanceCodePoint();yn(e)?(e.advanceCodePoint(),xr(t,e)):e.advanceCodePoint()}}function Ai(t,e){for(;Ye(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();let n="";for(;;){if(e.source.codePointAt(e.cursor)===void 0){const a=[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new Ue(xe.UnexpectedEOFInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","Unexpected EOF"],a)),a}if(e.source.codePointAt(e.cursor)===41)return e.advanceCodePoint(),[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];if(Ye(e.source.codePointAt(e.cursor)??-1)){for(e.advanceCodePoint();Ye(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===void 0){const a=[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new Ue(xe.UnexpectedEOFInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","Consume as much whitespace as possible","Unexpected EOF"],a)),a}return e.source.codePointAt(e.cursor)===41?(e.advanceCodePoint(),[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}]):(ds(t,e),[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0])}const s=e.source.codePointAt(e.cursor);if(s===34||s===39||s===40||(r=s??-1)===11||r===127||0<=r&&r<=8||14<=r&&r<=31){ds(t,e);const a=[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new Ue(xe.UnexpectedCharacterInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token",`Unexpected U+0022 QUOTATION MARK ("), U+0027 APOSTROPHE ('), U+0028 LEFT PARENTHESIS (() or non-printable code point`],a)),a}if(s===92){if(yn(e)){e.advanceCodePoint(),n+=String.fromCodePoint(xr(t,e));continue}ds(t,e);const a=[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new Ue(xe.InvalidEscapeSequenceInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],a)),a}e.source.codePointAt(e.cursor)===0||wn(e.source.codePointAt(e.cursor)??-1)?(n+=String.fromCodePoint(vn),e.advanceCodePoint()):(n+=e.source[e.cursor],e.advanceCodePoint())}var r}function gs(t,e){const n=Ar(t,e);if(e.source.codePointAt(e.cursor)!==40)return[v.Ident,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}];if(xi(n)){e.advanceCodePoint();let r=0;for(;;){const s=Ye(e.source.codePointAt(e.cursor)??-1),a=Ye(e.source.codePointAt(e.cursor+1)??-1);if(s&&a){r+=1,e.advanceCodePoint(1);continue}const o=s?e.source.codePointAt(e.cursor+1):e.source.codePointAt(e.cursor);if(o===34||o===39)return r>0&&e.unreadCodePoint(r),[v.Function,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}];break}return Ai(t,e)}return e.advanceCodePoint(),[v.Function,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}]}function Mi(t){return!(t.source.codePointAt(t.cursor)!==117&&t.source.codePointAt(t.cursor)!==85||t.source.codePointAt(t.cursor+1)!==mn||t.source.codePointAt(t.cursor+2)!==63&&!Ze(t.source.codePointAt(t.cursor+2)??-1))}function Pi(t,e){e.advanceCodePoint(2);const n=[],r=[];let s;for(;(s=e.source.codePointAt(e.cursor))!==void 0&&n.length<6&&Ze(s);)n.push(s),e.advanceCodePoint();for(;(s=e.source.codePointAt(e.cursor))!==void 0&&n.length<6&&s===63;)r.length===0&&r.push(...n),n.push(48),r.push(70),e.advanceCodePoint();if(!r.length&&e.source.codePointAt(e.cursor)===yt&&Ze(e.source.codePointAt(e.cursor+1)??-1))for(e.advanceCodePoint();(s=e.source.codePointAt(e.cursor))!==void 0&&r.length<6&&Ze(s);)r.push(s),e.advanceCodePoint();if(!r.length){const i=parseInt(String.fromCodePoint(...n),16);return[v.UnicodeRange,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{startOfRange:i,endOfRange:i}]}const a=parseInt(String.fromCodePoint(...n),16),o=parseInt(String.fromCodePoint(...r),16);return[v.UnicodeRange,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{startOfRange:a,endOfRange:o}]}function Je(t,e){const n=Sa(t),r=[];for(;!n.endOfFile();)r.push(n.nextToken());return r.push(n.nextToken()),r}function Sa(t,e){const n=t.css.valueOf(),r=t.unicodeRangesAllowed??!1,s=new Fi(n),a={onParseError:Di};return{nextToken:function(){s.resetRepresentation();const o=s.source.codePointAt(s.cursor);if(o===void 0)return[v.EOF,"",-1,-1,void 0];if(o===47&&bi(s))return Ni(a,s);if(r&&(o===117||o===85)&&Mi(s))return Pi(0,s);if(bn(o))return gs(a,s);if(Vt(o))return ps(a,s);switch(o){case 44:return s.advanceCodePoint(),[v.Comma,",",s.representationStart,s.representationEnd,void 0];case 58:return s.advanceCodePoint(),[v.Colon,":",s.representationStart,s.representationEnd,void 0];case 59:return s.advanceCodePoint(),[v.Semicolon,";",s.representationStart,s.representationEnd,void 0];case 40:return s.advanceCodePoint(),[v.OpenParen,"(",s.representationStart,s.representationEnd,void 0];case 41:return s.advanceCodePoint(),[v.CloseParen,")",s.representationStart,s.representationEnd,void 0];case 91:return s.advanceCodePoint(),[v.OpenSquare,"[",s.representationStart,s.representationEnd,void 0];case 93:return s.advanceCodePoint(),[v.CloseSquare,"]",s.representationStart,s.representationEnd,void 0];case 123:return s.advanceCodePoint(),[v.OpenCurly,"{",s.representationStart,s.representationEnd,void 0];case 125:return s.advanceCodePoint(),[v.CloseCurly,"}",s.representationStart,s.representationEnd,void 0];case 39:case 34:return Si(a,s);case 35:return Ei(a,s);case mn:case 46:return Fa(s)?ps(a,s):(s.advanceCodePoint(),[v.Delim,s.source[s.representationStart],s.representationStart,s.representationEnd,{value:s.source[s.representationStart]}]);case gn:case dn:case 12:case 9:case 32:return ki(s);case yt:return Fa(s)?ps(a,s):wi(s)?(s.advanceCodePoint(3),[v.CDC,"-->",s.representationStart,s.representationEnd,void 0]):Fr(0,s)?gs(a,s):(s.advanceCodePoint(),[v.Delim,"-",s.representationStart,s.representationEnd,{value:"-"}]);case 60:return pi(s)?(s.advanceCodePoint(4),[v.CDO,"<!--",s.representationStart,s.representationEnd,void 0]):(s.advanceCodePoint(),[v.Delim,"<",s.representationStart,s.representationEnd,{value:"<"}]);case 64:if(s.advanceCodePoint(),Fr(0,s)){const i=Ar(a,s);return[v.AtKeyword,s.source.slice(s.representationStart,s.representationEnd+1),s.representationStart,s.representationEnd,{value:String.fromCodePoint(...i)}]}return[v.Delim,"@",s.representationStart,s.representationEnd,{value:"@"}];case 92:{if(yn(s))return gs(a,s);s.advanceCodePoint();const i=[v.Delim,"\\",s.representationStart,s.representationEnd,{value:"\\"}];return a.onParseError(new Ue(xe.InvalidEscapeSequenceAfterBackslash,s.representationStart,s.representationEnd,["4.3.1. Consume a token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],i)),i}}return s.advanceCodePoint(),[v.Delim,s.source[s.representationStart],s.representationStart,s.representationEnd,{value:s.source[s.representationStart]}]},endOfFile:function(){return s.source.codePointAt(s.cursor)===void 0}}}function Di(){}function xa(t,e){const n=[];for(const i of e)n.push(i.codePointAt(0));const r=Bi(n);r[0]===101&&Mr(r,0,r[0]);const s=String.fromCodePoint(...r),a=t[4].signCharacter==="+"?t[4].signCharacter:"",o=t[4].value.toString();t[1]=`${a}${o}${s}`,t[4].unit=e}function Bi(t){let e=0;t[0]===yt&&t[1]===yt?e=2:t[0]===yt&&t[1]?(e=2,bn(t[1])||(e+=Mr(t,1,t[1]))):bn(t[0])?e=1:(e=1,e+=Mr(t,0,t[0]));for(let n=e;n<t.length;n++)fs(t[n])||(n+=Mr(t,n,t[n]));return t}function Mr(t,e,n){const r=n.toString(16),s=[];for(const o of r)s.push(o.codePointAt(0));const a=t[e+1];return e===t.length-1||a&&Ze(a)?(t.splice(e,1,92,...s,32),1+s.length):(t.splice(e,1,92,...s),s.length)}const Oi=Object.values(v);function ms(t){return!!Array.isArray(t)&&!(t.length<4)&&!!Oi.includes(t[0])&&typeof t[1]=="string"&&typeof t[2]=="number"&&typeof t[3]=="number"}function ut(t){if(!t)return!1;switch(t[0]){case v.Dimension:case v.Number:case v.Percentage:return!0;default:return!1}}function Aa(t){if(!t)return!1;switch(t[0]){case v.Whitespace:case v.Comment:return!0;default:return!1}}function Xt(t){return!!t&&t[0]===v.Comma}function Ma(t){return!!t&&t[0]===v.Comment}function Pr(t){return!!t&&t[0]===v.Delim}function et(t){return!!t&&t[0]===v.Dimension}function we(t){return!!t&&t[0]===v.EOF}function Ri(t){return!!t&&t[0]===v.Function}function Wi(t){return!!t&&t[0]===v.Hash}function ct(t){return!!t&&t[0]===v.Ident}function T(t){return!!t&&t[0]===v.Number}function Q(t){return!!t&&t[0]===v.Percentage}function vs(t){return!!t&&t[0]===v.Whitespace}function Pa(t){return!!t&&t[0]===v.OpenParen}function Ti(t){return!!t&&t[0]===v.CloseParen}function Li(t){return!!t&&t[0]===v.OpenSquare}function Ii(t){return!!t&&t[0]===v.OpenCurly}var Kt;function Da(t){let e=t.slice();return(n,r,s)=>{let a=-1;for(let o=e.indexOf(r);o<e.length&&(a=n.indexOf(e[o]),a===-1||a<s);o++);return a===-1||a===s&&r===n[s]&&(a++,a>=n.length)?-1:(e=n.slice(),a)}}function Dr(t,e){const n=e[0];if(Pa(n)||Ii(n)||Li(n)){const r=Hi(t,e);return{advance:r.advance,node:r.node}}if(Ri(n)){const r=_i(t,e);return{advance:r.advance,node:r.node}}if(vs(n)){const r=Oa(t,e);return{advance:r.advance,node:r.node}}if(Ma(n)){const r=zi(t,e);return{advance:r.advance,node:r.node}}return{advance:1,node:new _(n)}}(function(t){t.Function="function",t.SimpleBlock="simple-block",t.Whitespace="whitespace",t.Comment="comment",t.Token="token"})(Kt||(Kt={}));class Ba{constructor(){j(this,"value",[])}indexOf(e){return this.value.indexOf(e)}at(e){if(typeof e=="number")return e<0&&(e=this.value.length+e),this.value[e]}forEach(e,n){if(this.value.length===0)return;const r=Da(this.value);let s=0;for(;s<this.value.length;){const a=this.value[s];let o;if(n&&(o={...n}),e({node:a,parent:this,state:o},s)===!1)return!1;if(s=r(this.value,a,s),s===-1)break}}walk(e,n){this.value.length!==0&&this.forEach((r,s)=>e(r,s)!==!1&&(!("walk"in r.node)||!this.value.includes(r.node)||r.node.walk(e,r.state)!==!1)&&void 0,n)}}class Mt extends Ba{constructor(e,n,r){super(),j(this,"type",Kt.Function),j(this,"name"),j(this,"endToken"),this.name=e,this.endToken=n,this.value=r}getName(){return this.name[4].value}normalize(){we(this.endToken)&&(this.endToken=[v.CloseParen,")",-1,-1,void 0])}tokens(){return we(this.endToken)?[this.name,...this.value.flatMap(e=>e.tokens())]:[this.name,...this.value.flatMap(e=>e.tokens()),this.endToken]}toString(){const e=this.value.map(n=>ms(n)?oe(n):n.toString()).join("");return oe(this.name)+e+oe(this.endToken)}toJSON(){return{type:this.type,name:this.getName(),tokens:this.tokens(),value:this.value.map(e=>e.toJSON())}}isFunctionNode(){return Mt.isFunctionNode(this)}static isFunctionNode(e){return!!e&&e instanceof Mt&&e.type===Kt.Function}}function _i(t,e){const n=[];let r=1;for(;;){const s=e[r];if(!s||we(s))return t.onParseError(new Kn("Unexpected EOF while consuming a function.",e[0][2],e[e.length-1][3],["5.4.9. Consume a function","Unexpected EOF"])),{advance:e.length,node:new Mt(e[0],s,n)};if(Ti(s))return{advance:r+1,node:new Mt(e[0],s,n)};if(Aa(s)){const o=Ra(t,e.slice(r));r+=o.advance,n.push(...o.nodes);continue}const a=Dr(t,e.slice(r));r+=a.advance,n.push(a.node)}}class Hn extends Ba{constructor(e,n,r){super(),j(this,"type",Kt.SimpleBlock),j(this,"startToken"),j(this,"endToken"),this.startToken=e,this.endToken=n,this.value=r}normalize(){if(we(this.endToken)){const e=$i(this.startToken);e&&(this.endToken=e)}}tokens(){return we(this.endToken)?[this.startToken,...this.value.flatMap(e=>e.tokens())]:[this.startToken,...this.value.flatMap(e=>e.tokens()),this.endToken]}toString(){const e=this.value.map(n=>ms(n)?oe(n):n.toString()).join("");return oe(this.startToken)+e+oe(this.endToken)}toJSON(){return{type:this.type,startToken:this.startToken,tokens:this.tokens(),value:this.value.map(e=>e.toJSON())}}isSimpleBlockNode(){return Hn.isSimpleBlockNode(this)}static isSimpleBlockNode(e){return!!e&&e instanceof Hn&&e.type===Kt.SimpleBlock}}function Hi(t,e){const n=yi(e[0][0]);if(!n)throw new Error("Failed to parse, a mirror variant must exist for all block open tokens.");const r=[];let s=1;for(;;){const a=e[s];if(!a||we(a))return t.onParseError(new Kn("Unexpected EOF while consuming a simple block.",e[0][2],e[e.length-1][3],["5.4.8. Consume a simple block","Unexpected EOF"])),{advance:e.length,node:new Hn(e[0],a,r)};if(a[0]===n)return{advance:s+1,node:new Hn(e[0],a,r)};if(Aa(a)){const i=Ra(t,e.slice(s));s+=i.advance,r.push(...i.nodes);continue}const o=Dr(t,e.slice(s));s+=o.advance,r.push(o.node)}}class Ht{constructor(e){j(this,"type",Kt.Whitespace),j(this,"value"),this.value=e}tokens(){return this.value}toString(){return oe(...this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isWhitespaceNode(){return Ht.isWhitespaceNode(this)}static isWhitespaceNode(e){return!!e&&e instanceof Ht&&e.type===Kt.Whitespace}}function Oa(t,e){let n=0;for(;;){const r=e[n];if(!vs(r))return{advance:n,node:new Ht(e.slice(0,n))};n++}}class zn{constructor(e){j(this,"type",Kt.Comment),j(this,"value"),this.value=e}tokens(){return[this.value]}toString(){return oe(this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isCommentNode(){return zn.isCommentNode(this)}static isCommentNode(e){return!!e&&e instanceof zn&&e.type===Kt.Comment}}function zi(t,e){return{advance:1,node:new zn(e[0])}}function Ra(t,e){const n=[];let r=0;for(;;)if(vs(e[r])){const s=Oa(0,e.slice(r));r+=s.advance,n.push(s.node)}else{if(!Ma(e[r]))return{advance:r,nodes:n};n.push(new zn(e[r])),r++}}class _{constructor(e){j(this,"type",Kt.Token),j(this,"value"),this.value=e}tokens(){return[this.value]}toString(){return this.value[1]}toJSON(){return{type:this.type,tokens:this.tokens()}}isTokenNode(){return _.isTokenNode(this)}static isTokenNode(e){return!!e&&e instanceof _&&e.type===Kt.Token}}function Ui(t,e){const n={onParseError:()=>{}},r=[...t];we(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const s=Dr(n,r);if(we(r[Math.min(s.advance,r.length-1)]))return s.node;n.onParseError(new Kn("Expected EOF after parsing a component value.",t[0][2],t[t.length-1][3],["5.3.9. Parse a component value","Expected EOF"]))}function Gi(t,e){const n={onParseError:e?.onParseError??(()=>{})},r=[...t];if(t.length===0)return[];we(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const s=[];let a=[],o=0;for(;;){if(!r[o]||we(r[o]))return a.length&&s.push(a),s;if(Xt(r[o])){s.push(a),a=[],o++;continue}const i=Dr(n,t.slice(o));a.push(i.node),o+=i.advance}}function ji(t,e,n){if(t.length===0)return;const r=Da(t);let s=0;for(;s<t.length;){const a=t[s];if(e({node:a,parent:{value:t},state:void 0},s)===!1)return!1;if(s=r(t,a,s),s===-1)break}}function qi(t,e,n){t.length!==0&&ji(t,(r,s)=>e(r,s)!==!1&&(!("walk"in r.node)||!t.includes(r.node)||r.node.walk(e,r.state)!==!1)&&void 0)}function Vi(t,e){for(let n=0;n<t.length;n++)qi(t[n],(r,s)=>{if(typeof s!="number")return;const a=e(r.node);a&&(Array.isArray(a)?r.parent.value.splice(s,1,...a):r.parent.value.splice(s,1,a))});return t}function Xi(t){return Hn.isSimpleBlockNode(t)}function Zt(t){return Mt.isFunctionNode(t)}function Yt(t){return Ht.isWhitespaceNode(t)}function Jt(t){return zn.isCommentNode(t)}function Qe(t){return Yt(t)||Jt(t)}function R(t){return _.isTokenNode(t)}const Ki=/[A-Z]/g;function Bt(t){return t.replace(Ki,e=>String.fromCharCode(e.charCodeAt(0)+32))}const Zi={cm:"px",in:"px",mm:"px",pc:"px",pt:"px",px:"px",q:"px",deg:"deg",grad:"deg",rad:"deg",turn:"deg",ms:"s",s:"s",hz:"hz",khz:"hz"},Yi=new Map([["cm",t=>t],["mm",t=>10*t],["q",t=>40*t],["in",t=>t/2.54],["pc",t=>t/2.54*6],["pt",t=>t/2.54*72],["px",t=>t/2.54*96]]),Br=new Map([["deg",t=>t],["grad",t=>t/.9],["rad",t=>t/180*Math.PI],["turn",t=>t/360]]),Zn=new Map([["deg",t=>.9*t],["grad",t=>t],["rad",t=>.9*t/180*Math.PI],["turn",t=>.9*t/360]]),Ji=new Map([["hz",t=>t],["khz",t=>t/1e3]]),Qi=new Map([["cm",t=>2.54*t],["mm",t=>25.4*t],["q",t=>25.4*t*4],["in",t=>t],["pc",t=>6*t],["pt",t=>72*t],["px",t=>96*t]]),tl=new Map([["hz",t=>1e3*t],["khz",t=>t]]),el=new Map([["cm",t=>t/10],["mm",t=>t],["q",t=>4*t],["in",t=>t/25.4],["pc",t=>t/25.4*6],["pt",t=>t/25.4*72],["px",t=>t/25.4*96]]),nl=new Map([["ms",t=>t],["s",t=>t/1e3]]),rl=new Map([["cm",t=>t/6*2.54],["mm",t=>t/6*25.4],["q",t=>t/6*25.4*4],["in",t=>t/6],["pc",t=>t],["pt",t=>t/6*72],["px",t=>t/6*96]]),sl=new Map([["cm",t=>t/72*2.54],["mm",t=>t/72*25.4],["q",t=>t/72*25.4*4],["in",t=>t/72],["pc",t=>t/72*6],["pt",t=>t],["px",t=>t/72*96]]),al=new Map([["cm",t=>t/96*2.54],["mm",t=>t/96*25.4],["q",t=>t/96*25.4*4],["in",t=>t/96],["pc",t=>t/96*6],["pt",t=>t/96*72],["px",t=>t]]),ol=new Map([["cm",t=>t/4/10],["mm",t=>t/4],["q",t=>t],["in",t=>t/4/25.4],["pc",t=>t/4/25.4*6],["pt",t=>t/4/25.4*72],["px",t=>t/4/25.4*96]]),Wa=new Map([["deg",t=>180*t/Math.PI],["grad",t=>180*t/Math.PI/.9],["rad",t=>t],["turn",t=>180*t/Math.PI/360]]),il=new Map([["ms",t=>1e3*t],["s",t=>t]]),Yn=new Map([["deg",t=>360*t],["grad",t=>360*t/.9],["rad",t=>360*t/180*Math.PI],["turn",t=>t]]),Ta=new Map([["cm",Yi],["mm",el],["q",ol],["in",Qi],["pc",rl],["pt",sl],["px",al],["ms",nl],["s",il],["deg",Br],["grad",Zn],["rad",Wa],["turn",Yn],["hz",Ji],["khz",tl]]);function Lt(t,e){if(!et(t)||!et(e))return e;const n=Bt(t[4].unit),r=Bt(e[4].unit);if(n===r)return e;const s=Ta.get(r);if(!s)return e;const a=s.get(n);if(!a)return e;const o=a(e[4].value),i=[v.Dimension,"",e[2],e[3],{...e[4],signCharacter:o<0?"-":void 0,type:Number.isInteger(o)?F.Integer:F.Number,value:o}];return xa(i,t[4].unit),i}function ll(t){if(!et(t))return t;const e=Bt(t[4].unit),n=Zi[e];if(e===n)return t;const r=Ta.get(e);if(!r)return t;const s=r.get(n);if(!s)return t;const a=s(t[4].value),o=[v.Dimension,"",t[2],t[3],{...t[4],signCharacter:a<0?"-":void 0,type:Number.isInteger(a)?F.Integer:F.Number,value:a}];return xa(o,n),o}function ul(t){if(t.length!==2)return-1;const e=t[0].value;let n=t[1].value;if(T(e)&&T(n)){const r=e[4].value+n[4].value;return new _([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===F.Integer&&n[4].type===F.Integer?F.Integer:F.Number}])}if(Q(e)&&Q(n)){const r=e[4].value+n[4].value;return new _([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(et(e)&&et(n)&&(n=Lt(e,n),Bt(e[4].unit)===Bt(n[4].unit))){const r=e[4].value+n[4].value;return new _([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===F.Integer&&n[4].type===F.Integer?F.Integer:F.Number,unit:e[4].unit}])}return-1}function cl(t){if(t.length!==2)return-1;const e=t[0].value,n=t[1].value;if(T(e)&&T(n)){const r=e[4].value/n[4].value;return new _([v.Number,r.toString(),e[2],n[3],{value:r,type:Number.isInteger(r)?F.Integer:F.Number}])}if(Q(e)&&T(n)){const r=e[4].value/n[4].value;return new _([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(et(e)&&T(n)){const r=e[4].value/n[4].value;return new _([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:Number.isInteger(r)?F.Integer:F.Number,unit:e[4].unit}])}return-1}function tn(t){return!!t&&typeof t=="object"&&"inputs"in t&&Array.isArray(t.inputs)&&"operation"in t}function It(t){if(t===-1)return-1;const e=[];for(let n=0;n<t.inputs.length;n++){const r=t.inputs[n];if(R(r)){e.push(r);continue}const s=It(r);if(s===-1)return-1;e.push(s)}return t.operation(e)}function hl(t){if(t.length!==2)return-1;const e=t[0].value,n=t[1].value;if(T(e)&&T(n)){const r=e[4].value*n[4].value;return new _([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===F.Integer&&n[4].type===F.Integer?F.Integer:F.Number}])}if(Q(e)&&T(n)){const r=e[4].value*n[4].value;return new _([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(T(e)&&Q(n)){const r=e[4].value*n[4].value;return new _([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(et(e)&&T(n)){const r=e[4].value*n[4].value;return new _([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===F.Integer&&n[4].type===F.Integer?F.Integer:F.Number,unit:e[4].unit}])}if(T(e)&&et(n)){const r=e[4].value*n[4].value;return new _([v.Dimension,r.toString()+n[4].unit,e[2],n[3],{value:r,type:e[4].type===F.Integer&&n[4].type===F.Integer?F.Integer:F.Number,unit:n[4].unit}])}return-1}function $n(t,e){for(let n=0;n<t.length;n++){const r=t[n];if(!R(r))continue;const s=r.value;if(!ct(s))continue;const a=Bt(s[4].value);switch(a){case"e":t.splice(n,1,new _([v.Number,Math.E.toString(),s[2],s[3],{value:Math.E,type:F.Number}]));break;case"pi":t.splice(n,1,new _([v.Number,Math.PI.toString(),s[2],s[3],{value:Math.PI,type:F.Number}]));break;case"infinity":t.splice(n,1,new _([v.Number,"infinity",s[2],s[3],{value:1/0,type:F.Number}]));break;case"-infinity":t.splice(n,1,new _([v.Number,"-infinity",s[2],s[3],{value:-1/0,type:F.Number}]));break;case"nan":t.splice(n,1,new _([v.Number,"NaN",s[2],s[3],{value:Number.NaN,type:F.Number}]));break;default:if(e.has(a)){const o=e.get(a);t.splice(n,1,new _(o))}}}return t}function Or(t){if(t.length!==1)return-1;const e=t[0].value;return ut(e)?t[0]:-1}function Ae(t,e,n){return et(e)?Jn(t,e[4].unit,n):Q(e)?fl(t,n):T(e)?ye(t,n):-1}function Jn(t,e,n){const r=t.tokens();return{inputs:[new _([v.Dimension,n.toString()+e,r[0][2],r[r.length-1][3],{value:n,type:Number.isInteger(n)?F.Integer:F.Number,unit:e}])],operation:Or}}function fl(t,e){const n=t.tokens();return{inputs:[new _([v.Percentage,e.toString()+"%",n[0][2],n[n.length-1][3],{value:e}])],operation:Or}}function ye(t,e){const n=t.tokens();return{inputs:[new _([v.Number,e.toString(),n[0][2],n[n.length-1][3],{value:e,type:Number.isInteger(e)?F.Integer:F.Number}])],operation:Or}}function pl(t,e){const n=e.value;return T(n)?Jn(t,"rad",Math.acos(n[4].value)):-1}function dl(t,e){const n=e.value;return T(n)?Jn(t,"rad",Math.asin(n[4].value)):-1}function gl(t,e){const n=e.value;return T(n)?Jn(t,"rad",Math.atan(n[4].value)):-1}function Rr(t){return et(t)||T(t)}function bs(t){if(t.length===0)return!0;const e=t[0];if(!ut(e))return!1;if(t.length===1)return!0;if(et(e)){const n=Bt(e[4].unit);for(let r=1;r<t.length;r++){const s=t[r];if(e[0]!==s[0]||n!==Bt(s[4].unit))return!1}return!0}for(let n=1;n<t.length;n++){const r=t[n];if(e[0]!==r[0])return!1}return!0}function Me(t,e){return!!ut(t)&&(et(t)?t[0]===e[0]&&Bt(t[4].unit)===Bt(e[4].unit):t[0]===e[0])}function ml(t,e,n){const r=e.value;if(!Rr(r))return-1;const s=Lt(r,n.value);return Me(r,s)?Jn(t,"rad",Math.atan2(r[4].value,s[4].value)):-1}function vl(t,e,n){const r=e.value;return!ut(r)||!n.rawPercentages&&Q(r)?-1:Ae(t,r,Math.abs(r[4].value))}function bl(t,e,n,r,s){if(!R(e)||!R(n)||!R(r))return-1;const a=e.value;if(!ut(a)||!s.rawPercentages&&Q(a))return-1;const o=Lt(a,n.value);if(!Me(a,o))return-1;const i=Lt(a,r.value);return Me(a,i)?Ae(t,a,Math.max(a[4].value,Math.min(o[4].value,i[4].value))):-1}function wl(t,e){const n=e.value;if(!Rr(n))return-1;let r=n[4].value;if(et(n))switch(n[4].unit.toLowerCase()){case"rad":break;case"deg":r=Br.get("rad")(n[4].value);break;case"grad":r=Zn.get("rad")(n[4].value);break;case"turn":r=Yn.get("rad")(n[4].value);break;default:return-1}return r=Math.cos(r),ye(t,r)}function yl(t,e){const n=e.value;return T(n)?ye(t,Math.exp(n[4].value)):-1}function $l(t,e,n){if(!e.every(R))return-1;const r=e[0].value;if(!ut(r)||!n.rawPercentages&&Q(r))return-1;const s=e.map(i=>Lt(r,i.value));if(!bs(s))return-1;const a=s.map(i=>i[4].value),o=Math.hypot(...a);return Ae(t,r,o)}function La(t,e,n){if(!e.every(R))return-1;const r=e[0].value;if(!ut(r)||!n.rawPercentages&&Q(r))return-1;const s=e.map(i=>Lt(r,i.value));if(!bs(s))return-1;const a=s.map(i=>i[4].value),o=Math.max(...a);return Ae(t,r,o)}function Ia(t,e,n){if(!e.every(R))return-1;const r=e[0].value;if(!ut(r)||!n.rawPercentages&&Q(r))return-1;const s=e.map(i=>Lt(r,i.value));if(!bs(s))return-1;const a=s.map(i=>i[4].value),o=Math.min(...a);return Ae(t,r,o)}function Nl(t,e,n){const r=e.value;if(!ut(r))return-1;const s=Lt(r,n.value);if(!Me(r,s))return-1;let a;return a=s[4].value===0?Number.NaN:Number.isFinite(r[4].value)&&(Number.isFinite(s[4].value)||(s[4].value!==Number.POSITIVE_INFINITY||r[4].value!==Number.NEGATIVE_INFINITY&&!Object.is(0*r[4].value,-0))&&(s[4].value!==Number.NEGATIVE_INFINITY||r[4].value!==Number.POSITIVE_INFINITY&&!Object.is(0*r[4].value,0)))?Number.isFinite(s[4].value)?(r[4].value%s[4].value+s[4].value)%s[4].value:r[4].value:Number.NaN,Ae(t,r,a)}function El(t,e,n){const r=e.value,s=n.value;return!T(r)||!Me(r,s)?-1:ye(t,Math.pow(r[4].value,s[4].value))}function Cl(t,e,n){const r=e.value;if(!ut(r))return-1;const s=Lt(r,n.value);if(!Me(r,s))return-1;let a;return a=s[4].value===0?Number.NaN:Number.isFinite(r[4].value)?Number.isFinite(s[4].value)?r[4].value%s[4].value:r[4].value:Number.NaN,Ae(t,r,a)}function kl(t,e,n,r,s){const a=n.value;if(!ut(a)||!s.rawPercentages&&Q(a))return-1;const o=Lt(a,r.value);if(!Me(a,o))return-1;let i;if(o[4].value===0)i=Number.NaN;else if(Number.isFinite(a[4].value)||Number.isFinite(o[4].value))if(!Number.isFinite(a[4].value)&&Number.isFinite(o[4].value))i=a[4].value;else if(Number.isFinite(a[4].value)&&!Number.isFinite(o[4].value))switch(e){case"down":i=a[4].value<0?-1/0:Object.is(-0,0*a[4].value)?-0:0;break;case"up":i=a[4].value>0?1/0:Object.is(0,0*a[4].value)?0:-0;break;default:i=Object.is(0,0*a[4].value)?0:-0}else if(Number.isFinite(o[4].value))switch(e){case"down":i=Math.floor(a[4].value/o[4].value)*o[4].value;break;case"up":i=Math.ceil(a[4].value/o[4].value)*o[4].value;break;case"to-zero":i=Math.trunc(a[4].value/o[4].value)*o[4].value;break;default:{let l=Math.floor(a[4].value/o[4].value)*o[4].value,u=Math.ceil(a[4].value/o[4].value)*o[4].value;if(l>u){const m=l;l=u,u=m}const c=Math.abs(a[4].value-l),f=Math.abs(a[4].value-u);i=c===f?u:c<f?l:u;break}}else i=a[4].value;else i=Number.NaN;return Ae(t,a,i)}function Fl(t,e,n){const r=e.value;return!ut(r)||!n.rawPercentages&&Q(r)?-1:ye(t,Math.sign(r[4].value))}function Sl(t,e){const n=e.value;if(!Rr(n))return-1;let r=n[4].value;if(et(n))switch(Bt(n[4].unit)){case"rad":break;case"deg":r=Br.get("rad")(n[4].value);break;case"grad":r=Zn.get("rad")(n[4].value);break;case"turn":r=Yn.get("rad")(n[4].value);break;default:return-1}return r=Math.sin(r),ye(t,r)}function xl(t,e){const n=e.value;return T(n)?ye(t,Math.sqrt(n[4].value)):-1}function Al(t,e){const n=e.value;if(!Rr(n))return-1;const r=n[4].value;let s=0,a=n[4].value;if(et(n))switch(Bt(n[4].unit)){case"rad":s=Wa.get("deg")(r);break;case"deg":s=r,a=Br.get("rad")(r);break;case"grad":s=Zn.get("deg")(r),a=Zn.get("rad")(r);break;case"turn":s=Yn.get("deg")(r),a=Yn.get("rad")(r);break;default:return-1}const o=s/90;return a=s%90==0&&o%2!=0?o>0?1/0:-1/0:Math.tan(a),ye(t,a)}function Ml(t){if(t.length!==2)return-1;const e=t[0].value;let n=t[1].value;if(T(e)&&T(n)){const r=e[4].value-n[4].value;return new _([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===F.Integer&&n[4].type===F.Integer?F.Integer:F.Number}])}if(Q(e)&&Q(n)){const r=e[4].value-n[4].value;return new _([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(et(e)&&et(n)&&(n=Lt(e,n),Bt(e[4].unit)===Bt(n[4].unit))){const r=e[4].value-n[4].value;return new _([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===F.Integer&&n[4].type===F.Integer?F.Integer:F.Number,unit:e[4].unit}])}return-1}function Pl(t,e){if(e.length===1){const n=e[0];if(!n||!R(n))return-1;const r=n.value;return T(r)?ye(t,Math.log(r[4].value)):-1}if(e.length===2){const n=e[0];if(!n||!R(n))return-1;const r=n.value;if(!T(r))return-1;const s=e[1];if(!s||!R(s))return-1;const a=s.value;return T(a)?ye(t,Math.log(r[4].value)/Math.log(a[4].value)):-1}return-1}const Dl=/^none$/i;function ws(t){if(Array.isArray(t)){const n=t.filter(r=>!(Yt(r)&&Jt(r)));return n.length===1&&ws(n[0])}if(!R(t))return!1;const e=t.value;return!!ct(e)&&Dl.test(e[4].value)}function Bl(t,e,n,r,s,a){const o=n.value;if(!ut(o))return-1;const i=Lt(o,r.value);if(!Me(o,i))return-1;let l,u=null;if(s&&(u=Lt(o,s.value),!Me(o,u)))return-1;if(Number.isFinite(o[4].value))if(Number.isFinite(i[4].value))if(u&&(!Number.isFinite(u[4].value)||u[4].value<=0))l=o[4].value;else{const c=Ol(Rl([e,oe(o),oe(i),s?`by ${s.toString()}`:""].join(",")),a.randomSeed);let f=o[4].value,m=i[4].value;if(f>m&&([f,m]=[m,f]),u){const d=Math.abs(f-m),$=c();l=f+Math.floor(d/u[4].value*$)*u[4].value}else{const d=c();l=Number((d*(m-f)+f).toFixed(5))}}else l=Number.NaN;else l=Number.NaN;return Ae(t,o,l)}function Ol(t=.34944106645296036,e=.19228640875738723,n=.8784393832007205,r=.04850964319275053){return()=>{const s=((t|=0)+(e|=0)|0)+(r|=0)|0;return r=r+1|0,t=e^e>>>9,e=(n|=0)+(n<<3)|0,n=(n=n<<21|n>>>11)+s|0,(s>>>0)/4294967296}}function Rl(t){let e=0,n=0,r=0;e=~e;for(let s=0,a=t.length;s<a;s++)r=255&(e^t.charCodeAt(s)),n=+("0x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substring(9*r,9*r+8)),e=e>>>8^n;return~e>>>0}const ys=new Map([["abs",function(t,e,n){return $e(t,e,n,vl)}],["acos",function(t,e,n){return $e(t,e,n,pl)}],["asin",function(t,e,n){return $e(t,e,n,dl)}],["atan",function(t,e,n){return $e(t,e,n,gl)}],["atan2",function(t,e,n){return Wr(t,e,n,ml)}],["calc",Ot],["clamp",function(t,e,n){const r=$n([...t.value.filter($=>!Qe($))],e),s=[],a=[],o=[];{let $=s;for(let N=0;N<r.length;N++){const b=r[N];if(R(b)&&Xt(b.value)){if($===o)return-1;if($===a){$=o;continue}if($===s){$=a;continue}return-1}$.push(b)}}const i=ws(s),l=ws(o);if(i&&l)return Ot(Qt(a),e,n);const u=It(Ot(Qt(a),e,n));if(u===-1)return-1;if(i){const $=It(Ot(Qt(o),e,n));return $===-1?-1:Ia((c=u,f=$,new Mt([v.Function,"min(",-1,-1,{value:"min"}],[v.CloseParen,")",-1,-1,void 0],[c,new _([v.Comma,",",-1,-1,void 0]),f])),[u,$],n)}if(l){const $=It(Ot(Qt(s),e,n));return $===-1?-1:La(Ll($,u),[$,u],n)}var c,f;const m=It(Ot(Qt(s),e,n));if(m===-1)return-1;const d=It(Ot(Qt(o),e,n));return d===-1?-1:bl(t,m,u,d,n)}],["cos",function(t,e,n){return $e(t,e,n,wl)}],["exp",function(t,e,n){return $e(t,e,n,yl)}],["hypot",function(t,e,n){return Tr(t,t.value,e,n,$l)}],["log",function(t,e,n){return Tr(t,t.value,e,n,Pl)}],["max",function(t,e,n){return Tr(t,t.value,e,n,La)}],["min",function(t,e,n){return Tr(t,t.value,e,n,Ia)}],["mod",function(t,e,n){return Wr(t,e,n,Nl)}],["pow",function(t,e,n){return Wr(t,e,n,El)}],["random",function(t,e,n){const r=t.value.filter(f=>!Qe(f));let s="";const a=[],o=[];for(let f=0;f<r.length;f++){const m=r[f];if(!s&&o.length===0&&R(m)&&ct(m.value)){const d=m.value[4].value.toLowerCase();if(d==="per-element"||d.startsWith("--")){s=d;const $=r[f+1];if(!R($)||!Xt($.value))return-1;f++;continue}}if(R(m)&&Xt(m.value)){const d=r[f+1];if(o.length>0&&R(d)&&ct(d.value)){const $=d.value[4].value.toLowerCase();if($==="by"||$.startsWith("--")){a.push(...r.slice(f+2));break}}}o.push(m)}const i=Ha(o,e,n);if(i===-1)return-1;const[l,u]=i;let c=null;return a.length&&(c=_a(a,e,n),c===-1)?-1:Bl(t,s,l,u,c,n)}],["rem",function(t,e,n){return Wr(t,e,n,Cl)}],["round",function(t,e,n){const r=$n([...t.value.filter(c=>!Qe(c))],e);let s="",a=!1;const o=[],i=[];{let c=o;for(let f=0;f<r.length;f++){const m=r[f];if(!s&&o.length===0&&i.length===0&&R(m)&&ct(m.value)){const d=m.value[4].value.toLowerCase();if(Tl.has(d)){s=d;continue}}if(R(m)&&Xt(m.value)){if(c===i)return-1;if(c===o&&s&&o.length===0)continue;if(c===o){a=!0,c=i;continue}return-1}c.push(m)}}const l=It(Ot(Qt(o),e,n));if(l===-1)return-1;a||i.length!==0||i.push(new _([v.Number,"1",-1,-1,{value:1,type:F.Integer}]));const u=It(Ot(Qt(i),e,n));return u===-1?-1:(s||(s="nearest"),kl(t,s,l,u,n))}],["sign",function(t,e,n){return $e(t,e,n,Fl)}],["sin",function(t,e,n){return $e(t,e,n,Sl)}],["sqrt",function(t,e,n){return $e(t,e,n,xl)}],["tan",function(t,e,n){return $e(t,e,n,Al)}]]);function Ot(t,e,n){const r=$n([...t.value.filter(a=>!Qe(a))],e);if(r.length===1&&R(r[0]))return{inputs:[r[0]],operation:Or};let s=0;for(;s<r.length;){const a=r[s];if(Xi(a)&&Pa(a.startToken)){const o=Ot(a,e,n);if(o===-1)return-1;r.splice(s,1,o)}else if(Zt(a)){const o=ys.get(a.getName().toLowerCase());if(!o)return-1;const i=o(a,e,n);if(i===-1)return-1;r.splice(s,1,i)}else s++}if(s=0,r.length===1&&tn(r[0]))return r[0];for(;s<r.length;){const a=r[s];if(!a||!R(a)&&!tn(a)){s++;continue}const o=r[s+1];if(!o||!R(o)){s++;continue}const i=o.value;if(!Pr(i)||i[4].value!=="*"&&i[4].value!=="/"){s++;continue}const l=r[s+2];if(!l||!R(l)&&!tn(l))return-1;i[4].value!=="*"?i[4].value!=="/"?s++:r.splice(s,3,{inputs:[a,l],operation:cl}):r.splice(s,3,{inputs:[a,l],operation:hl})}if(s=0,r.length===1&&tn(r[0]))return r[0];for(;s<r.length;){const a=r[s];if(!a||!R(a)&&!tn(a)){s++;continue}const o=r[s+1];if(!o||!R(o)){s++;continue}const i=o.value;if(!Pr(i)||i[4].value!=="+"&&i[4].value!=="-"){s++;continue}const l=r[s+2];if(!l||!R(l)&&!tn(l))return-1;i[4].value!=="+"?i[4].value!=="-"?s++:r.splice(s,3,{inputs:[a,l],operation:Ml}):r.splice(s,3,{inputs:[a,l],operation:ul})}return r.length===1&&tn(r[0])?r[0]:-1}function $e(t,e,n,r){const s=_a(t.value,e,n);return s===-1?-1:r(t,s,n)}function _a(t,e,n){const r=It(Ot(Qt($n([...t.filter(s=>!Qe(s))],e)),e,n));return r===-1?-1:r}function Wr(t,e,n,r){const s=Ha(t.value,e,n);if(s===-1)return-1;const[a,o]=s;return r(t,a,o,n)}function Ha(t,e,n){const r=$n([...t.filter(l=>!Qe(l))],e),s=[],a=[];{let l=s;for(let u=0;u<r.length;u++){const c=r[u];if(R(c)&&Xt(c.value)){if(l===a)return-1;if(l===s){l=a;continue}return-1}l.push(c)}}const o=It(Ot(Qt(s),e,n));if(o===-1)return-1;const i=It(Ot(Qt(a),e,n));return i===-1?-1:[o,i]}function Tr(t,e,n,r,s){const a=Wl(t.value,n,r);return a===-1?-1:s(t,a,r)}function Wl(t,e,n){const r=$n([...t.filter(a=>!Qe(a))],e),s=[];{const a=[];let o=[];for(let i=0;i<r.length;i++){const l=r[i];R(l)&&Xt(l.value)?(a.push(o),o=[]):o.push(l)}a.push(o);for(let i=0;i<a.length;i++){if(a[i].length===0)return-1;const l=It(Ot(Qt(a[i]),e,n));if(l===-1)return-1;s.push(l)}}return s}const Tl=new Set(["nearest","up","down","to-zero"]);function Qt(t){return new Mt([v.Function,"calc(",-1,-1,{value:"calc"}],[v.CloseParen,")",-1,-1,void 0],t)}function Ll(t,e){return new Mt([v.Function,"max(",-1,-1,{value:"max"}],[v.CloseParen,")",-1,-1,void 0],[t,new _([v.Comma,",",-1,-1,void 0]),e])}function Il(t){if(t===-1)return-1;if(Zt(t))return t;const e=t.value;return ut(e)&&Number.isNaN(e[4].value)?T(e)?new Mt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new _([v.Ident,"NaN",e[2],e[3],{value:"NaN"}])]):et(e)?new Mt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new _([v.Ident,"NaN",e[2],e[3],{value:"NaN"}]),new Ht([[v.Whitespace," ",e[2],e[3],void 0]]),new _([v.Delim,"*",e[2],e[3],{value:"*"}]),new Ht([[v.Whitespace," ",e[2],e[3],void 0]]),new _([v.Dimension,"1"+e[4].unit,e[2],e[3],{value:1,type:F.Integer,unit:e[4].unit}])]):Q(e)?new Mt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new _([v.Ident,"NaN",e[2],e[3],{value:"NaN"}]),new Ht([[v.Whitespace," ",e[2],e[3],void 0]]),new _([v.Delim,"*",e[2],e[3],{value:"*"}]),new Ht([[v.Whitespace," ",e[2],e[3],void 0]]),new _([v.Percentage,"1%",e[2],e[3],{value:1}])]):-1:t}function _l(t){if(t===-1)return-1;if(Zt(t))return t;const e=t.value;if(!ut(e)||Number.isFinite(e[4].value)||Number.isNaN(e[4].value))return t;let n="";return Number.NEGATIVE_INFINITY===e[4].value&&(n="-"),T(e)?new Mt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new _([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}])]):et(e)?new Mt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new _([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}]),new Ht([[v.Whitespace," ",e[2],e[3],void 0]]),new _([v.Delim,"*",e[2],e[3],{value:"*"}]),new Ht([[v.Whitespace," ",e[2],e[3],void 0]]),new _([v.Dimension,"1"+e[4].unit,e[2],e[3],{value:1,type:F.Integer,unit:e[4].unit}])]):new Mt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new _([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}]),new Ht([[v.Whitespace," ",e[2],e[3],void 0]]),new _([v.Delim,"*",e[2],e[3],{value:"*"}]),new Ht([[v.Whitespace," ",e[2],e[3],void 0]]),new _([v.Percentage,"1%",e[2],e[3],{value:1}])])}function Hl(t){if(t===-1)return-1;if(Zt(t))return t;const e=t.value;return ut(e)&&Object.is(-0,e[4].value)&&(e[1]==="-0"||(Q(e)?e[1]="-0%":et(e)?e[1]="-0"+e[4].unit:e[1]="-0")),t}function zl(t,e=13){if(t===-1)return-1;if(e<=0||Zt(t))return t;const n=t.value;if(!ut(n)||Number.isInteger(n[4].value))return t;const r=Number(n[4].value.toFixed(e)).toString();return T(n)?n[1]=r:Q(n)?n[1]=r+"%":et(n)&&(n[1]=r+n[4].unit),t}function Ul(t){return t===-1?-1:(Zt(t)||et(t.value)&&(t.value=ll(t.value)),t)}function Gl(t,e){let n=t;return e!=null&&e.toCanonicalUnits&&(n=Ul(n)),n=zl(n,e?.precision),n=Hl(n),e!=null&&e.censorIntoStandardRepresentableValues||(n=Il(n),n=_l(n)),n}function jl(t){const e=new Map;if(!t)return e;for(const[n,r]of t)if(ms(r))e.set(n,r);else if(typeof r=="string"){const s=Sa({css:r}),a=s.nextToken();if(s.nextToken(),!s.endOfFile()||!ut(a))continue;e.set(n,a)}return e}function $t(t,e){return Qn(Gi(Je({css:t}),{}),e).map(n=>n.map(r=>oe(...r.tokens())).join("")).join(",")}function Qn(t,e){const n=jl(e?.globals);return Vi(t,r=>{if(!Zt(r))return;const s=ys.get(r.getName().toLowerCase());if(!s)return;const a=Gl(It(s(r,n,e??{})),e);return a!==-1?a:void 0})}const Lr=new Set(ys.keys()),at="computedValue",X="specifiedValue",za="(?:0|[1-9]\\d*)",ql="clamp|max|min",Vl="mod|rem|round",Xl="a?(?:cos|sin|tan)|atan2",Kl="exp|hypot|log|pow|sqrt",Zl="abs|sign",Ua=`${ql}|${Vl}|${Xl}|${Kl}|${Zl}`,$s=`calc|${Ua}`,Yl=`var|${$s}`,Ns="deg|g?rad|turn",te=`[+-]?(?:${za}(?:\\.\\d*)?|\\.\\d+)(?:e-?${za})?`,g="none",Rt=`${te}%`,Ga=`^(?:${Ua})\\($`,Ir=`^(?:${$s})\\(|(?<=[*\\/\\s\\(])(?:${$s})\\(`,Jl=`^(?:${Yl})\\(`,tr="^var\\(|(?<=[*\\/\\s\\(])var\\(",_r=`(?:\\s*\\/\\s*(?:${te}|${Rt}|${g}))?`,ja=`(?:\\s*,\\s*(?:${te}|${Rt}))?`,qa="(?:ok)?l(?:ab|ch)|color|hsla?|hwb|rgba?",Ql="[a-z]+|#[\\da-f]{3}|#[\\da-f]{4}|#[\\da-f]{6}|#[\\da-f]{8}",Va="(?:ok)?lch|hsl|hwb",Xa="(?:de|in)creasing|longer|shorter",tu=`${te}(?:${Ns})?`,Ka=`(?:${te}(?:${Ns})?|${g})`,er=`(?:${te}|${Rt}|${g})`,eu=`(?:${Va})(?:\\s(?:${Xa})\\shue)?`,nu=`(${Va})(?:\\s(${Xa})\\shue)?`,Za="(?:ok)?lab",ru="(?:ok)?lch",Ya="srgb(?:-linear)?",Ja=`(?:a98|prophoto)-rgb|display-p3|rec2020|${Ya}`,Es="xyz(?:-d(?:50|65))?",Cs=`${eu}|${Za}|${Ya}|${Es}`,ot="color(",Ge="color-mix(",nr=`(?:${qa})\\(\\s*from\\s+`,su=`(${qa})\\(\\s*from\\s+`,Hr="var(",Qa=`(?:${Ja}|${Es})(?:\\s+${er}){3}${_r}`,to=`^${nr}|(?<=[\\s])${nr}`,ks=`${Ka}(?:\\s+${er}){2}${_r}`,eo=`${tu}(?:\\s*,\\s*${Rt}){2}${ja}`,Fs=`(?:${er}\\s+){2}${Ka}${_r}`,zr=`${er}(?:\\s+${er}){2}${_r}`,no=`(?:${te}(?:\\s*,\\s*${te}){2}|${Rt}(?:\\s*,\\s*${Rt}){2})${ja}`,en=`${Ql}|hsla?\\(\\s*${eo}\\s*\\)|rgba?\\(\\s*${no}\\s*\\)|(?:hsla?|hwb)\\(\\s*${ks}\\s*\\)|(?:(?:ok)?lab|rgba?)\\(\\s*${zr}\\s*\\)|(?:ok)?lch\\(\\s*${Fs}\\s*\\)|color\\(\\s*${Qa}\\s*\\)`,Ur=`(?:${en})(?:\\s+${Rt})?`,Gr=`color-mix\\(\\s*in\\s+(?:${Cs})\\s*,\\s*${Ur}\\s*,\\s*${Ur}\\s*\\)`,au=`color-mix\\(\\s*in\\s+(${Cs})\\s*,\\s*(${Ur})\\s*,\\s*(${Ur})\\s*\\)`,{CloseParen:ou,Comment:ro,Dimension:iu,EOF:lu,Function:uu,OpenParen:cu,Whitespace:so}=v,hu=16,ao=100,fu=new RegExp(Ir),pu=new RegExp(tr),oo=/\s[*+/-]\s/,du=new RegExp(Ga),jr=new RegExp(Jl),qr=new RegExp(`^(${te})([a-z]+)$`),Nn=new RegExp(`^(${te})([a-z]+|%)$`),je=new RegExp(`^(${te})%$`),En=new Xn({max:4096});var Pe,Cn,kn,De,Fn,Sn,Ne,nn,rn,Be,Oe,qe,Ve,Re,sn,an;class gu{constructor(){L(this,Pe),L(this,Cn),L(this,kn),L(this,De),L(this,Fn),L(this,Sn),L(this,Ne),L(this,nn),L(this,rn),L(this,Be),L(this,Oe),L(this,qe),L(this,Ve),L(this,Re),L(this,sn),L(this,an),k(this,Pe,!1),k(this,Cn,[]),k(this,kn,[]),k(this,De,!1),k(this,Fn,[]),k(this,Sn,[]),k(this,Ne,!1),k(this,nn,[]),k(this,rn,[]),k(this,Be,[]),k(this,Oe,[]),k(this,qe,!1),k(this,Ve,[]),k(this,Re,[]),k(this,sn,[]),k(this,an,[])}get hasNum(){return h(this,Pe)}set hasNum(e){k(this,Pe,!!e)}get numSum(){return h(this,Cn)}get numMul(){return h(this,kn)}get hasPct(){return h(this,De)}set hasPct(e){k(this,De,!!e)}get pctSum(){return h(this,Fn)}get pctMul(){return h(this,Sn)}get hasDim(){return h(this,Ne)}set hasDim(e){k(this,Ne,!!e)}get dimSum(){return h(this,nn)}get dimSub(){return h(this,rn)}get dimMul(){return h(this,Be)}get dimDiv(){return h(this,Oe)}get hasEtc(){return h(this,qe)}set hasEtc(e){k(this,qe,!!e)}get etcSum(){return h(this,Ve)}get etcSub(){return h(this,Re)}get etcMul(){return h(this,sn)}get etcDiv(){return h(this,an)}clear(){k(this,Pe,!1),k(this,Cn,[]),k(this,kn,[]),k(this,De,!1),k(this,Fn,[]),k(this,Sn,[]),k(this,Ne,!1),k(this,nn,[]),k(this,rn,[]),k(this,Be,[]),k(this,Oe,[]),k(this,qe,!1),k(this,Ve,[]),k(this,Re,[]),k(this,sn,[]),k(this,an,[])}sort(e=[]){const n=[...e];return n.length>1&&n.sort((r,s)=>{let a;if(Nn.test(r)&&Nn.test(s)){const[,o,i]=r.match(Nn),[,l,u]=s.match(Nn);i===u?Number(o)===Number(l)?a=0:Number(o)>Number(l)?a=1:a=-1:i>u?a=1:a=-1}else r===s?a=0:r>s?a=1:a=-1;return a}),n}multiply(){const e=[];let n;if(h(this,Pe)){n=1;for(const r of h(this,kn))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;!h(this,De)&&!h(this,Ne)&&!this.hasEtc&&e.push(n)}if(h(this,De)){h(this,Pe)||(n=1);for(const r of h(this,Sn))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;Number.isFinite(n)&&(n=`${n}%`),!h(this,Ne)&&!this.hasEtc&&e.push(n)}if(h(this,Ne)){let r,s,a;h(this,Be).length&&(h(this,Be).length===1?[s]=h(this,Be):s=`${this.sort(h(this,Be)).join(" * ")}`),h(this,Oe).length&&(h(this,Oe).length===1?[a]=h(this,Oe):a=`${this.sort(h(this,Oe)).join(" * ")}`),Number.isFinite(n)?(s?a?a.includes("*")?r=$t(`calc(${n} * ${s} / (${a}))`,{toCanonicalUnits:!0}):r=$t(`calc(${n} * ${s} / ${a})`,{toCanonicalUnits:!0}):r=$t(`calc(${n} * ${s})`,{toCanonicalUnits:!0}):a.includes("*")?r=$t(`calc(${n} / (${a}))`,{toCanonicalUnits:!0}):r=$t(`calc(${n} / ${a})`,{toCanonicalUnits:!0}),e.push(r.replace(/^calc/,""))):(!e.length&&n!==void 0&&e.push(n),s?(a?a.includes("*")?r=$t(`calc(${s} / (${a}))`,{toCanonicalUnits:!0}):r=$t(`calc(${s} / ${a})`,{toCanonicalUnits:!0}):r=$t(`calc(${s})`,{toCanonicalUnits:!0}),e.length?e.push("*",r.replace(/^calc/,"")):e.push(r.replace(/^calc/,""))):(r=$t(`calc(${a})`,{toCanonicalUnits:!0}),e.length?e.push("/",r.replace(/^calc/,"")):e.push("1","/",r.replace(/^calc/,""))))}if(h(this,qe)){if(h(this,sn).length){!e.length&&n!==void 0&&e.push(n);const r=this.sort(h(this,sn)).join(" * ");e.length?e.push(`* ${r}`):e.push(`${r}`)}if(h(this,an).length){const r=this.sort(h(this,an)).join(" * ");r.includes("*")?e.length?e.push(`/ (${r})`):e.push(`1 / (${r})`):e.length?e.push(`/ ${r}`):e.push(`1 / ${r}`)}}return e.join(" ")||null}sum(){const e=[];if(h(this,Pe)){let n=0;for(const r of h(this,Cn))if(n+=r,!Number.isFinite(n)||Number.isNaN(n))break;e.push(n)}if(h(this,De)){let n=0;for(const r of h(this,Fn))if(n+=r,!Number.isFinite(n)||Number.isNaN(n))break;Number.isFinite(n)&&(n=`${n}%`),e.length?e.push(`+ ${n}`):e.push(n)}if(h(this,Ne)){let n,r,s;h(this,nn).length&&(r=h(this,nn).join(" + ")),h(this,rn).length&&(s=h(this,rn).join(" + ")),r?s?s.includes("-")?n=$t(`calc(${r} - (${s}))`,{toCanonicalUnits:!0}):n=$t(`calc(${r} - ${s})`,{toCanonicalUnits:!0}):n=$t(`calc(${r})`,{toCanonicalUnits:!0}):n=$t(`calc(-1 * (${s}))`,{toCanonicalUnits:!0}),e.length?e.push("+",n.replace(/^calc/,"")):e.push(n.replace(/^calc/,""))}if(h(this,qe)){if(h(this,Ve).length){const n=this.sort(h(this,Ve)).map(r=>{let s;return oo.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?s=`(${r})`:s=r,s}).join(" + ");e.length?h(this,Ve).length>1?e.push(`+ (${n})`):e.push(`+ ${n}`):e.push(`${n}`)}if(h(this,Re).length){const n=this.sort(h(this,Re)).map(r=>{let s;return oo.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?s=`(${r})`:s=r,s}).join(" + ");e.length?h(this,Re).length>1?e.push(`- (${n})`):e.push(`- ${n}`):h(this,Re).length>1?e.push(`-1 * (${n})`):e.push(`-1 * ${n}`)}}return e.join(" ")||null}}Pe=new WeakMap,Cn=new WeakMap,kn=new WeakMap,De=new WeakMap,Fn=new WeakMap,Sn=new WeakMap,Ne=new WeakMap,nn=new WeakMap,rn=new WeakMap,Be=new WeakMap,Oe=new WeakMap,qe=new WeakMap,Ve=new WeakMap,Re=new WeakMap,sn=new WeakMap,an=new WeakMap;const io=(t=[],e=!1)=>{if(t.length<3)return null;const n=t.shift(),r=t.pop();if(t.length===1){const[l]=t;return`${n}${l}${r}`}const s=[],a=new gu;let o;for(let l=0,u=t.length;l<u;l++){const c=t[l];if(c==="*"||c==="/")o=c;else if(c==="+"||c==="-"){const f=a.multiply();s.push(f,c),a.clear(),o=null}else{switch(o){case"/":{const f=Number(c);if(Number.isFinite(f))a.hasNum=!0,a.numMul.push(1/f);else if(je.test(c)){const[,m]=c.match(je);a.hasPct=!0,a.pctMul.push(ao*ao/Number(m))}else qr.test(c)?(a.hasDim=!0,a.dimDiv.push(c)):(a.hasEtc=!0,a.etcDiv.push(c));break}case"*":default:{const f=Number(c);if(Number.isFinite(f))a.hasNum=!0,a.numMul.push(f);else if(je.test(c)){const[,m]=c.match(je);a.hasPct=!0,a.pctMul.push(Number(m))}else qr.test(c)?(a.hasDim=!0,a.dimMul.push(c)):(a.hasEtc=!0,a.etcMul.push(c))}}if(l===u-1){const f=a.multiply();s.push(f),a.clear(),o=null}}}let i;if(e&&(s.includes("+")||s.includes("-"))){const l=[];a.clear(),o=null;for(let u=0,c=s.length;u<c;u++){const f=s[u];if(f==="+"||f==="-")o=f;else{switch(o){case"-":{const m=Number(f);if(Number.isFinite(m))a.hasNum=!0,a.numSum.push(-1*m);else if(je.test(f)){const[,d]=f.match(je);a.hasPct=!0,a.pctSum.push(-1*Number(d))}else qr.test(f)?(a.hasDim=!0,a.dimSub.push(f)):(a.hasEtc=!0,a.etcSub.push(f));break}case"+":default:{const m=Number(f);if(Number.isFinite(m))a.hasNum=!0,a.numSum.push(m);else if(je.test(f)){const[,d]=f.match(je);a.hasPct=!0,a.pctSum.push(Number(d))}else qr.test(f)?(a.hasDim=!0,a.dimSum.push(f)):(a.hasEtc=!0,a.etcSum.push(f))}}if(u===c-1){const m=a.sum();l.push(m),a.clear(),o=null}}}i=l.join(" ")}else i=s.join(" ");return`${n}${i}${r}`},mu=(t,e={})=>{const{format:n}=e;if(V(t)){if(!jr.test(t)||n!==X)return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string`);const r=`{serializeCalc:${t},opt:${Ct(e)}}`;if(En.has(r))return En.get(r);const s=Je({css:t}).map(i=>{const[l,u]=i;let c;return l!==so&&l!==ro&&(c=u),c}).filter(i=>i);let a=s.findLastIndex(i=>/\($/.test(i));for(;a;){const i=s.findIndex((c,f)=>c===")"&&f>a),l=s.slice(a,i+1);let u=io(l);jr.test(u)&&(u=$t(u,{toCanonicalUnits:!0})),s.splice(a,i-a+1,u),a=s.findLastIndex(c=>/\($/.test(c))}const o=io(s,!0);return r&&En.set(r,o),o},lo=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const[,n,,,r={}]=t,{unit:s,value:a}=r,{dimension:o={}}=e;if(s==="px")return n;let i;if(s&&Number.isFinite(a)){let l;Object.hasOwnProperty.call(o,s)?l=o[s]:typeof o.callback=="function"&&(l=o.callback(s)),l=Number(l),Number.isFinite(l)&&(i=`${a*l}px`)}return i??null},vu=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{format:n}=e,r=new Set;let s=0;const a=[];for(;t.length;){const o=t.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i,l]=o;switch(i){case iu:{let u;n===X&&!r.has(s)?u=l:(u=lo(o,e),u||(u=l)),a.push(u);break}case uu:case cu:{a.push(l),s++,du.test(l)&&r.add(s);break}case ou:{a.length&&a[a.length-1]===" "?a.splice(-1,1,l):a.push(l),r.has(s)&&r.delete(s),s--;break}case so:{if(a.length){const u=a[a.length-1];!u.endsWith("(")&&u!==" "&&a.push(l)}break}default:i!==ro&&i!==lu&&a.push(l)}}return a},rr=(t,e={})=>{const{format:n,dimension:r={}}=e;if(V(t)){if(pu.test(t)){if(n===X)return t;throw new SyntaxError(`Unexpected token ${Hr} found.`)}else if(!fu.test(t))return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string`);let s;if(typeof r.callback!="function"&&(s=`{cssCalc:${t},opt:${Ct(e)}}`,En.has(s)))return En.get(s);let a;if(r){const o=Je({css:t}),i=vu(o,e);a=$t(i.join(""),{toCanonicalUnits:!0})}else a=$t(t,{toCanonicalUnits:!0});if(jr.test(t)){if(Nn.test(a)){const[,o,i]=a.match(Nn);a=`${A(Number(o),hu)}${i}`}a&&!jr.test(a)&&n===X&&(a=`calc(${a})`)}return s&&En.set(s,a),a},{CloseParen:uo,Comment:bu,EOF:wu,Ident:yu,Whitespace:$u}=v,co=new RegExp(Ir),ho=new RegExp(tr),Vr=new Xn({max:4096});function fo(t,e={}){if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{customProperty:n={}}=e,r=[];for(;t.length;){const o=t.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i,l]=o;if(i===uo)break;if(l===Hr){const[u,c]=fo(t,e);t=u,c&&r.push(c)}else if(i===yu)if(l.startsWith("--")){if(Object.hasOwnProperty.call(n,l))r.push(n[l]);else if(typeof n.callback=="function"){const u=n.callback(l);u&&r.push(u)}}else l&&r.push(l)}let s;if(r.length>1){const o=r[r.length-1];s=cr(o)}let a;for(let o of r)if(o=o.trim(),ho.test(o)?(o=Ss(o,e),o&&(s?cr(o)&&(a=o):a=o)):co.test(o)?(o=rr(o,e),s?cr(o)&&(a=o):a=o):o&&!/^(?:inherit|initial|revert(?:-layer)?|unset)$/.test(o)&&(s?cr(o)&&(a=o):a=o),a)break;return[t,a]}function Nu(t,e={}){const n=[];for(;t.length;){const r=t.shift(),[s,a]=r;if(a===Hr){const[o,i]=fo(t,e);if(!i)return null;t=o,n.push(i)}else switch(s){case uo:{n.length&&n[n.length-1]===" "?n.splice(-1,1,a):n.push(a);break}case $u:{if(n.length){const o=n[n.length-1];!o.endsWith("(")&&o!==" "&&n.push(a)}break}default:s!==bu&&s!==wu&&n.push(a)}}return n}function Ss(t,e={}){const{customProperty:n={},format:r}=e;if(V(t)){if(!ho.test(t)||r===X)return t;t=t.trim()}else throw new TypeError(`${t} is not a string.`);let s;if(typeof n.callback!="function"&&(s=`{cssVar:${t},opt:${Ct(e)}}`,Vr.has(s)))return Vr.get(s);const a=Je({css:t}),o=Nu(a,e);if(Array.isArray(o)){let i=o.join("");return co.test(i)&&(i=rr(i,e)),s&&Vr.set(s,i),i}else return s&&Vr.set(s,null),null}function Nt(t,e){return[t[0]*e[0]+t[1]*e[1]+t[2]*e[2],t[3]*e[0]+t[4]*e[1]+t[5]*e[2],t[6]*e[0]+t[7]*e[1]+t[8]*e[2]]}const Eu=[.955473421488075,-.02309845494876471,.06325924320057072,-.0283697093338637,1.0099953980813041,.021041441191917323,.012314014864481998,-.020507649298898964,1.330365926242124];function Ee(t){return Nt(Eu,t)}const Cu=[1.0479297925449969,.022946870601609652,-.05019226628920524,.02962780877005599,.9904344267538799,-.017073799063418826,-.009243040646204504,.015055191490298152,.7518742814281371];function Ce(t){return Nt(Cu,t)}function po(t){let e=t[0]%360;const n=t[1]/100,r=t[2]/100;return e<0&&(e+=360),[xs(0,e,n,r),xs(8,e,n,r),xs(4,e,n,r)]}function xs(t,e,n,r){const s=(t+e/30)%12;return r-n*Math.min(r,1-r)*Math.max(-1,Math.min(s-3,9-s,1))}function ku(t){const e=t[0],n=t[1]/100,r=t[2]/100;if(n+r>=1){const o=n/(n+r);return[o,o,o]}const s=po([e,100,50]),a=1-n-r;return[s[0]*a+n,s[1]*a+n,s[2]*a+n]}function Fu(t){const e=t[2]*Math.PI/180;return[t[0],t[1]*Math.cos(e),t[1]*Math.sin(e)]}function Su(t){const e=180*Math.atan2(t[2],t[1])/Math.PI;return[t[0],Math.sqrt(Math.pow(t[1],2)+Math.pow(t[2],2)),e>=0?e:e+360]}const xn=[.3457/.3585,1,.2958/.3585];function go(t){const e=903.2962962962963,n=216/24389,r=(t[0]+16)/116,s=t[1]/500+r,a=r-t[2]/200;return[(Math.pow(s,3)>n?Math.pow(s,3):(116*s-16)/e)*xn[0],(t[0]>8?Math.pow((t[0]+16)/116,3):t[0]/e)*xn[1],(Math.pow(a,3)>n?Math.pow(a,3):(116*a-16)/e)*xn[2]]}function mo(t){const e=t[2]*Math.PI/180;return[t[0],t[1]*Math.cos(e),t[1]*Math.sin(e)]}function vo(t){const e=180*Math.atan2(t[2],t[1])/Math.PI;return[t[0],Math.sqrt(t[1]**2+t[2]**2),e>=0?e:e+360]}const xu=[1.2268798758459243,-.5578149944602171,.2813910456659647,-.0405757452148008,1.112286803280317,-.0717110580655164,-.0763729366746601,-.4214933324022432,1.5869240198367816],Au=[1,.3963377773761749,.2158037573099136,1,-.1055613458156586,-.0638541728258133,1,-.0894841775298119,-1.2914855480194092];function As(t){const e=Nt(Au,t);return Nt(xu,[e[0]**3,e[1]**3,e[2]**3])}function bo(t){const e=Ms(t[0]/xn[0]),n=Ms(t[1]/xn[1]);return[116*n-16,500*(e-n),200*(n-Ms(t[2]/xn[2]))]}const Mu=216/24389,Pu=24389/27;function Ms(t){return t>Mu?Math.cbrt(t):(Pu*t+16)/116}const Du=[.819022437996703,.3619062600528904,-.1288737815209879,.0329836539323885,.9292868615863434,.0361446663506424,.0481771893596242,.2642395317527308,.6335478284694309],Bu=[.210454268309314,.7936177747023054,-.0040720430116193,1.9779985324311684,-2.42859224204858,.450593709617411,.0259040424655478,.7827717124575296,-.8086757549230774];function Ps(t){const e=Nt(Du,t);return Nt(Bu,[Math.cbrt(e[0]),Math.cbrt(e[1]),Math.cbrt(e[2])])}const Ou=[30757411/17917100,-6372589/17917100,-4539589/17917100,-.666684351832489,1.616481236634939,467509/29648200,792561/44930125,-1921689/44930125,.942103121235474];const Ru=[446124/178915,-333277/357830,-72051/178915,-14852/17905,63121/35810,423/17905,11844/330415,-50337/660830,316169/330415];function Wu(t){return Nt(Ru,t)}const Tu=[1.3457868816471583,-.25557208737979464,-.05110186497554526,-.5446307051249019,1.5082477428451468,.02052744743642139,0,0,1.2119675456389452];const Lu=[1829569/896150,-506331/896150,-308931/896150,-851781/878810,1648619/878810,36519/878810,16779/1248040,-147721/1248040,1266979/1248040];const Iu=[12831/3959,-329/214,-1974/3959,-851781/878810,1648619/878810,36519/878810,705/12673,-2585/12673,705/667];function sr(t){return Nt(Iu,t)}const wo=1.09929682680944,_u=.018053968510807;function Ds(t){const e=t<0?-1:1,n=Math.abs(t);return n>_u?e*(wo*Math.pow(n,.45)-(wo-1)):4.5*t}function ar(t){return[Bs(t[0]),Bs(t[1]),Bs(t[2])]}function Bs(t){const e=t<0?-1:1,n=Math.abs(t);return n>.0031308?e*(1.055*Math.pow(n,1/2.4)-.055):12.92*t}function Hu(t){return ar(t)}const zu=1/512;function Os(t){const e=t<0?-1:1,n=Math.abs(t);return n>=zu?e*Math.pow(n,1/1.8):16*t}function Rs(t){const e=t<0?-1:1,n=Math.abs(t);return e*Math.pow(n,256/563)}const yo=1.09929682680944,Uu=.018053968510807;function Ws(t){const e=t<0?-1:1,n=Math.abs(t);return n<4.5*Uu?t/4.5:e*Math.pow((n+yo-1)/yo,1/.45)}const Gu=[63426534/99577255,20160776/139408157,47086771/278816314,26158966/99577255,.677998071518871,8267143/139408157,0,19567812/697040785,1.0609850577107909];function Xr(t){return[Ts(t[0]),Ts(t[1]),Ts(t[2])]}function Ts(t){const e=t<0?-1:1,n=Math.abs(t);return n<=.04045?t/12.92:e*Math.pow((n+.055)/1.055,2.4)}function ju(t){return Xr(t)}const qu=[608311/1250200,189793/714400,198249/1000160,35783/156275,247089/357200,198249/2500400,0,32229/714400,5220557/5000800];function Vu(t){return Nt(qu,t)}const Xu=16/512;function Ls(t){const e=t<0?-1:1,n=Math.abs(t);return n<=Xu?t/16:e*Math.pow(n,1.8)}const Ku=[.7977666449006423,.13518129740053308,.0313477341283922,.2880748288194013,.711835234241873,8993693872564e-17,0,0,.8251046025104602];function Is(t){const e=t<0?-1:1,n=Math.abs(t);return e*Math.pow(n,563/256)}const Zu=[573536/994567,263643/1420810,187206/994567,591459/1989134,6239551/9945670,374412/4972835,53769/1989134,351524/4972835,4929758/4972835];const Yu=[506752/1228815,87881/245763,12673/70218,87098/409605,175762/245763,12673/175545,7918/409605,87881/737289,1001167/1053270];function or(t){return Nt(Yu,t)}function Ju(t){const e=t[0],n=t[1],r=t[2],s=Math.max(e,n,r),a=Math.min(e,n,r),o=(a+s)/2,i=s-a;let l=NaN,u=0;if(Math.round(1e5*i)!==0){const c=Math.round(1e5*o);switch(u=c===0||c===1e5?0:(s-o)/Math.min(o,1-o),s){case e:l=(n-r)/i+(n<r?6:0);break;case n:l=(r-e)/i+2;break;case r:l=(e-n)/i+4}l*=60}return u<0&&(l+=180,u=Math.abs(u)),l>=360&&(l-=360),[l,100*u,100*o]}function Qu(t){const e=t[0],n=t[1],r=t[2],s=Math.max(e,n,r);let a=NaN;const o=s-Math.min(e,n,r);if(o!==0){switch(s){case e:a=(n-r)/o+(n<r?6:0);break;case n:a=(r-e)/o+2;break;case r:a=(e-n)/o+4}a*=60}return a>=360&&(a-=360),a}function tc(t){let e=t;return e=Xr(e),e=or(e),e=Ce(e),e}function _s(t){let e=t;return e=Ee(e),e=sr(e),e=ar(e),e}function ec(t){let e=t;return e=po(e),e=Xr(e),e=or(e),e=Ce(e),e}function nc(t){let e=t;return e=Ee(e),e=sr(e),e=ar(e),e=Ju(e),e}function rc(t){let e=t;return e=ku(e),e=Xr(e),e=or(e),e=Ce(e),e}function sc(t){let e=t;e=Ee(e),e=sr(e);const n=ar(e),r=Math.min(n[0],n[1],n[2]),s=1-Math.max(n[0],n[1],n[2]);return[Qu(n),100*r,100*s]}function ac(t){let e=t;return e=go(e),e}function oc(t){let e=t;return e=bo(e),e}function ic(t){let e=t;return e=Fu(e),e=go(e),e}function lc(t){let e=t;return e=bo(e),e=Su(e),e}function uc(t){let e=t;return e=As(e),e=Ce(e),e}function cc(t){let e=t;return e=Ee(e),e=Ps(e),e}function hc(t){let e=t;return e=mo(e),e=As(e),e=Ce(e),e}function $o(t){let e=t;return e=Ee(e),e=Ps(e),e=vo(e),e}function fc(t){let e=t;return e=or(e),e=Ce(e),e}function pc(t){let e=t;return e=Ee(e),e=sr(e),e}function dc(t){let e=t;var n;return e=[Is((n=e)[0]),Is(n[1]),Is(n[2])],e=Nt(Zu,e),e=Ce(e),e}function gc(t){let e=t;var n;return e=Ee(e),e=Nt(Lu,e),e=[Rs((n=e)[0]),Rs(n[1]),Rs(n[2])],e}function mc(t){let e=t;return e=ju(e),e=Vu(e),e=Ce(e),e}function vc(t){let e=t;return e=Ee(e),e=Wu(e),e=Hu(e),e}function bc(t){let e=t;var n;return e=[Ws((n=e)[0]),Ws(n[1]),Ws(n[2])],e=Nt(Gu,e),e=Ce(e),e}function wc(t){let e=t;var n;return e=Ee(e),e=Nt(Ou,e),e=[Ds((n=e)[0]),Ds(n[1]),Ds(n[2])],e}function yc(t){let e=t;var n;return e=[Ls((n=e)[0]),Ls(n[1]),Ls(n[2])],e=Nt(Ku,e),e}function $c(t){let e=t;var n;return e=Nt(Tu,e),e=[Os((n=e)[0]),Os(n[1]),Os(n[2])],e}function Nc(t){let e=t;return e=Ce(e),e}function Ec(t){let e=t;return e=Ee(e),e}function Cc(t){return t[0]>=-1e-4&&t[0]<=1.0001&&t[1]>=-1e-4&&t[1]<=1.0001&&t[2]>=-1e-4&&t[2]<=1.0001}function No(t){return[t[0]<0?0:t[0]>1?1:t[0],t[1]<0?0:t[1]>1?1:t[1],t[2]<0?0:t[2]>1?1:t[2]]}function kc(t,e,n){const r=t[0],s=t[2];let a=e(t);const o=e([r,0,s]);for(let i=0;i<4;i++){if(i>0){const u=n(a);u[0]=r,u[2]=s,a=e(u)}const l=Fc(o,a);if(!l)break;a=l}return No(a)}function Fc(t,e){let n=1/0,r=-1/0;const s=[0,0,0];for(let a=0;a<3;a++){const o=t[a],i=e[a]-o;s[a]=i;const l=0,u=1;if(i){const c=1/i,f=(l-o)*c,m=(u-o)*c;r=Math.max(Math.min(f,m),r),n=Math.min(Math.max(f,m),n)}else if(o<l||o>u)return!1}return!(r>n||n<0)&&(r<0&&(r=n),!!isFinite(r)&&[t[0]+s[0]*r,t[1]+s[1]*r,t[2]+s[2]*r])}const Sc={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};function Eo(t){const[e,n,r]=t.map(s=>s<=.03928?s/12.92:Math.pow((s+.055)/1.055,2.4));return .2126*e+.7152*n+.0722*r}function Co(t,e){const n=Eo(t),r=Eo(e);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)}var w,x;function Et(t){return[Number.isNaN(t[0])?0:t[0],Number.isNaN(t[1])?0:t[1],Number.isNaN(t[2])?0:t[2]]}function ko(t){switch(t.colorNotation){case w.HEX:case w.RGB:case w.sRGB:return{...t,colorNotation:w.XYZ_D50,channels:tc(Et(t.channels))};case w.Linear_sRGB:return{...t,colorNotation:w.XYZ_D50,channels:fc(Et(t.channels))};case w.Display_P3:return{...t,colorNotation:w.XYZ_D50,channels:mc(Et(t.channels))};case w.Rec2020:return{...t,colorNotation:w.XYZ_D50,channels:bc(Et(t.channels))};case w.A98_RGB:return{...t,colorNotation:w.XYZ_D50,channels:dc(Et(t.channels))};case w.ProPhoto_RGB:return{...t,colorNotation:w.XYZ_D50,channels:yc(Et(t.channels))};case w.HSL:return{...t,colorNotation:w.XYZ_D50,channels:ec(Et(t.channels))};case w.HWB:return{...t,colorNotation:w.XYZ_D50,channels:rc(Et(t.channels))};case w.Lab:return{...t,colorNotation:w.XYZ_D50,channels:ac(Et(t.channels))};case w.OKLab:return{...t,colorNotation:w.XYZ_D50,channels:uc(Et(t.channels))};case w.LCH:return{...t,colorNotation:w.XYZ_D50,channels:ic(Et(t.channels))};case w.OKLCH:return{...t,colorNotation:w.XYZ_D50,channels:hc(Et(t.channels))};case w.XYZ_D50:return{...t,colorNotation:w.XYZ_D50,channels:Et(t.channels)};case w.XYZ_D65:return{...t,colorNotation:w.XYZ_D50,channels:Nc(Et(t.channels))};default:throw new Error("Unsupported color notation")}}(function(t){t.A98_RGB="a98-rgb",t.Display_P3="display-p3",t.HEX="hex",t.HSL="hsl",t.HWB="hwb",t.LCH="lch",t.Lab="lab",t.Linear_sRGB="srgb-linear",t.OKLCH="oklch",t.OKLab="oklab",t.ProPhoto_RGB="prophoto-rgb",t.RGB="rgb",t.sRGB="srgb",t.Rec2020="rec2020",t.XYZ_D50="xyz-d50",t.XYZ_D65="xyz-d65"})(w||(w={})),function(t){t.ColorKeyword="color-keyword",t.HasAlpha="has-alpha",t.HasDimensionValues="has-dimension-values",t.HasNoneKeywords="has-none-keywords",t.HasNumberValues="has-number-values",t.HasPercentageAlpha="has-percentage-alpha",t.HasPercentageValues="has-percentage-values",t.HasVariableAlpha="has-variable-alpha",t.Hex="hex",t.LegacyHSL="legacy-hsl",t.LegacyRGB="legacy-rgb",t.NamedColor="named-color",t.RelativeColorSyntax="relative-color-syntax",t.ColorMix="color-mix",t.ContrastColor="contrast-color",t.Experimental="experimental"}(x||(x={}));const Fo=new Set([w.A98_RGB,w.Display_P3,w.HEX,w.Linear_sRGB,w.ProPhoto_RGB,w.RGB,w.sRGB,w.Rec2020,w.XYZ_D50,w.XYZ_D65]);function An(t,e){const n={...t};if(t.colorNotation!==e){const r=ko(n);switch(e){case w.HEX:case w.RGB:n.colorNotation=w.RGB,n.channels=_s(r.channels);break;case w.sRGB:n.colorNotation=w.sRGB,n.channels=_s(r.channels);break;case w.Linear_sRGB:n.colorNotation=w.Linear_sRGB,n.channels=pc(r.channels);break;case w.Display_P3:n.colorNotation=w.Display_P3,n.channels=vc(r.channels);break;case w.Rec2020:n.colorNotation=w.Rec2020,n.channels=wc(r.channels);break;case w.ProPhoto_RGB:n.colorNotation=w.ProPhoto_RGB,n.channels=$c(r.channels);break;case w.A98_RGB:n.colorNotation=w.A98_RGB,n.channels=gc(r.channels);break;case w.HSL:n.colorNotation=w.HSL,n.channels=nc(r.channels);break;case w.HWB:n.colorNotation=w.HWB,n.channels=sc(r.channels);break;case w.Lab:n.colorNotation=w.Lab,n.channels=oc(r.channels);break;case w.LCH:n.colorNotation=w.LCH,n.channels=lc(r.channels);break;case w.OKLCH:n.colorNotation=w.OKLCH,n.channels=$o(r.channels);break;case w.OKLab:n.colorNotation=w.OKLab,n.channels=cc(r.channels);break;case w.XYZ_D50:n.colorNotation=w.XYZ_D50,n.channels=r.channels;break;case w.XYZ_D65:n.colorNotation=w.XYZ_D65,n.channels=Ec(r.channels);break;default:throw new Error("Unsupported color notation")}}else n.channels=Et(t.channels);if(e===t.colorNotation)n.channels=Wt(t.channels,[0,1,2],n.channels,[0,1,2]);else if(Fo.has(e)&&Fo.has(t.colorNotation))n.channels=Wt(t.channels,[0,1,2],n.channels,[0,1,2]);else switch(e){case w.HSL:switch(t.colorNotation){case w.HWB:n.channels=Wt(t.channels,[0],n.channels,[0]);break;case w.Lab:case w.OKLab:n.channels=Wt(t.channels,[2],n.channels,[0]);break;case w.LCH:case w.OKLCH:n.channels=Wt(t.channels,[0,1,2],n.channels,[2,1,0])}break;case w.HWB:switch(t.colorNotation){case w.HSL:n.channels=Wt(t.channels,[0],n.channels,[0]);break;case w.LCH:case w.OKLCH:n.channels=Wt(t.channels,[0],n.channels,[2])}break;case w.Lab:case w.OKLab:switch(t.colorNotation){case w.HSL:n.channels=Wt(t.channels,[0],n.channels,[2]);break;case w.Lab:case w.OKLab:n.channels=Wt(t.channels,[0,1,2],n.channels,[0,1,2]);break;case w.LCH:case w.OKLCH:n.channels=Wt(t.channels,[0],n.channels,[0])}break;case w.LCH:case w.OKLCH:switch(t.colorNotation){case w.HSL:n.channels=Wt(t.channels,[0,1,2],n.channels,[2,1,0]);break;case w.HWB:n.channels=Wt(t.channels,[0],n.channels,[2]);break;case w.Lab:case w.OKLab:n.channels=Wt(t.channels,[0],n.channels,[0]);break;case w.LCH:case w.OKLCH:n.channels=Wt(t.channels,[0,1,2],n.channels,[0,1,2])}}return n.channels=xc(n.channels,e),n}function xc(t,e){const n=[...t];switch(e){case w.HSL:!Number.isNaN(n[1])&&ir(n[1],4)<=0&&(n[0]=NaN);break;case w.HWB:Math.max(0,ir(n[1],4))+Math.max(0,ir(n[2],4))>=100&&(n[0]=NaN);break;case w.LCH:!Number.isNaN(n[1])&&ir(n[1],4)<=0&&(n[2]=NaN);break;case w.OKLCH:!Number.isNaN(n[1])&&ir(n[1],6)<=0&&(n[2]=NaN)}return n}function Wt(t,e,n,r){const s=[...n];for(const a of e)Number.isNaN(t[e[a]])&&(s[r[a]]=NaN);return s}function So(t){const e=new Map;switch(t.colorNotation){case w.RGB:case w.HEX:e.set("r",tt(255*t.channels[0])),e.set("g",tt(255*t.channels[1])),e.set("b",tt(255*t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",tt(t.alpha));break;case w.HSL:e.set("h",tt(t.channels[0])),e.set("s",tt(t.channels[1])),e.set("l",tt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",tt(t.alpha));break;case w.HWB:e.set("h",tt(t.channels[0])),e.set("w",tt(t.channels[1])),e.set("b",tt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",tt(t.alpha));break;case w.Lab:case w.OKLab:e.set("l",tt(t.channels[0])),e.set("a",tt(t.channels[1])),e.set("b",tt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",tt(t.alpha));break;case w.LCH:case w.OKLCH:e.set("l",tt(t.channels[0])),e.set("c",tt(t.channels[1])),e.set("h",tt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",tt(t.alpha));break;case w.sRGB:case w.A98_RGB:case w.Display_P3:case w.Rec2020:case w.Linear_sRGB:case w.ProPhoto_RGB:e.set("r",tt(t.channels[0])),e.set("g",tt(t.channels[1])),e.set("b",tt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",tt(t.alpha));break;case w.XYZ_D50:case w.XYZ_D65:e.set("x",tt(t.channels[0])),e.set("y",tt(t.channels[1])),e.set("z",tt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",tt(t.alpha))}return e}function xo(t){const e=new Map(t);for(const[n,r]of t)Number.isNaN(r[4].value)&&e.set(n,tt(0));return e}function tt(t){return Number.isNaN(t)?[v.Number,"none",-1,-1,{value:Number.NaN,type:F.Number}]:[v.Number,t.toString(),-1,-1,{value:t,type:F.Number}]}function ir(t,e=7){if(Number.isNaN(t))return 0;const n=Math.pow(10,e);return Math.round(t*n)/n}function z(t,e,n,r){return Math.min(Math.max(t/e,n),r)}const Ac=/[A-Z]/g;function rt(t){return t.replace(Ac,e=>String.fromCharCode(e.charCodeAt(0)+32))}function lr(t,e,n){if(ct(t)&&rt(t[4].value)==="none")return n.syntaxFlags.add(x.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:NaN,type:F.Number}];if(Q(t)){e!==3&&n.syntaxFlags.add(x.HasPercentageValues);let r=z(t[4].value,100,-2147483647,2147483647);return e===3&&(r=z(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){e!==3&&n.syntaxFlags.add(x.HasNumberValues);let r=z(t[4].value,1,-2147483647,2147483647);return e===3&&(r=z(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}const Mc=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]);function Pc(t,e){const n=[],r=[],s=[],a=[];let o,i,l=!1,u=!1;const c={colorNotation:w.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([])};let f=n;for(let b=0;b<t.value.length;b++){let p=t.value[b];if(Yt(p)||Jt(p))for(;Yt(t.value[b+1])||Jt(t.value[b+1]);)b++;else if(f===n&&n.length&&(f=r),f===r&&r.length&&(f=s),R(p)&&Pr(p.value)&&p.value[4].value==="/"){if(f===a)return!1;f=a}else{if(Zt(p)){if(f===a&&rt(p.getName())==="var"){c.syntaxFlags.add(x.HasVariableAlpha),f.push(p);continue}if(!Lr.has(rt(p.getName())))return!1;const[[y]]=Qn([[p]],{censorIntoStandardRepresentableValues:!0,globals:i,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!y||!R(y)||!ut(y.value))return!1;Number.isNaN(y.value[4].value)&&(y.value[4].value=0),p=y}if(f===n&&n.length===0&&R(p)&&ct(p.value)&&Mc.has(rt(p.value[4].value))){if(l)return!1;l=rt(p.value[4].value),c.colorNotation=Dc(l),u&&(u.colorNotation!==c.colorNotation&&(u=An(u,c.colorNotation)),o=So(u),i=xo(o))}else if(f===n&&n.length===0&&R(p)&&ct(p.value)&&rt(p.value[4].value)==="from"){if(u||l)return!1;for(;Yt(t.value[b+1])||Jt(t.value[b+1]);)b++;if(b++,p=t.value[b],u=e(p),u===!1)return!1;u.syntaxFlags.has(x.Experimental)&&c.syntaxFlags.add(x.Experimental),c.syntaxFlags.add(x.RelativeColorSyntax)}else{if(!R(p))return!1;if(ct(p.value)&&o&&o.has(rt(p.value[4].value))){f.push(new _(o.get(rt(p.value[4].value))));continue}f.push(p)}}}if(!l||f.length!==1||n.length!==1||r.length!==1||s.length!==1||!R(n[0])||!R(r[0])||!R(s[0])||o&&!o.has("alpha"))return!1;const m=lr(n[0].value,0,c);if(!m||!T(m))return!1;const d=lr(r[0].value,1,c);if(!d||!T(d))return!1;const $=lr(s[0].value,2,c);if(!$||!T($))return!1;const N=[m,d,$];if(a.length===1)if(c.syntaxFlags.add(x.HasAlpha),R(a[0])){const b=lr(a[0].value,3,c);if(!b||!T(b))return!1;N.push(b)}else c.alpha=a[0];else if(o&&o.has("alpha")){const b=lr(o.get("alpha"),3,c);if(!b||!T(b))return!1;N.push(b)}return c.channels=[N[0][4].value,N[1][4].value,N[2][4].value],N.length===4&&(c.alpha=N[3][4].value),c}function Dc(t){switch(t){case"srgb":return w.sRGB;case"srgb-linear":return w.Linear_sRGB;case"display-p3":return w.Display_P3;case"a98-rgb":return w.A98_RGB;case"prophoto-rgb":return w.ProPhoto_RGB;case"rec2020":return w.Rec2020;case"xyz":case"xyz-d65":return w.XYZ_D65;case"xyz-d50":return w.XYZ_D50;default:throw new Error("Unknown color space name: "+t)}}const Bc=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","lab","oklab","xyz","xyz-d50","xyz-d65"]),Hs=new Set(["hsl","hwb","lch","oklch"]),Oc=new Set(["shorter","longer","increasing","decreasing"]);function Rc(t,e){let n=null,r=null,s=null,a=!1;for(let o=0;o<t.value.length;o++){const i=t.value[o];if(!Yt(i)&&!Jt(i)){if(R(i)&&ct(i.value)){if(!n&&rt(i.value[4].value)==="in"){n=i;continue}if(n&&!r){r=rt(i.value[4].value);continue}if(n&&r&&!s&&Hs.has(r)){s=rt(i.value[4].value);continue}if(n&&r&&s&&!a&&rt(i.value[4].value)==="hue"){a=!0;continue}return!1}return!(!R(i)||!Xt(i.value))&&!!r&&(s||a?!!(r&&s&&a&&Hs.has(r)&&Oc.has(s))&&Ao(r,s,zs(t.value.slice(o+1),e)):Bc.has(r)?Wc(r,zs(t.value.slice(o+1),e)):!!Hs.has(r)&&Ao(r,"shorter",zs(t.value.slice(o+1),e)))}}return!1}function zs(t,e){const n=[];let r=1,s=!1,a=!1;for(let l=0;l<t.length;l++){let u=t[l];if(!Yt(u)&&!Jt(u)){if(!R(u)||!Xt(u.value)){if(!s){const c=e(u);if(c){s=c;continue}}if(!a){if(Zt(u)&&Lr.has(rt(u.getName()))){if([[u]]=Qn([[u]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0}),!u||!R(u)||!ut(u.value))return!1;Number.isNaN(u.value[4].value)&&(u.value[4].value=0)}if(R(u)&&Q(u.value)&&u.value[4].value>=0){a=u.value[4].value;continue}}return!1}if(!s)return!1;n.push({color:s,percentage:a}),s=!1,a=!1}}if(s&&n.push({color:s,percentage:a}),n.length!==2)return!1;let o=n[0].percentage,i=n[1].percentage;return(o===!1||!(o<0||o>100))&&(i===!1||!(i<0||i>100))&&(o===!1&&i===!1?(o=50,i=50):o!==!1&&i===!1?i=100-o:o===!1&&i!==!1&&(o=100-i),(o!==0||i!==0)&&o!==!1&&i!==!1&&(o+i>100&&(o=o/(o+i)*100,i=i/(o+i)*100),o+i<100&&(r=(o+i)/100,o=o/(o+i)*100,i=i/(o+i)*100),{a:{color:n[0].color,percentage:o},b:{color:n[1].color,percentage:i},alphaMultiplier:r}))}function Wc(t,e){if(!e)return!1;const n=e.a.color,r=e.b.color,s=e.a.percentage/100;let a=n.channels,o=r.channels,i=w.RGB,l=n.alpha;if(typeof l!="number")return!1;let u=r.alpha;if(typeof u!="number")return!1;switch(l=Number.isNaN(l)?u:l,u=Number.isNaN(u)?l:u,t){case"srgb":i=w.RGB;break;case"srgb-linear":i=w.Linear_sRGB;break;case"display-p3":i=w.Display_P3;break;case"a98-rgb":i=w.A98_RGB;break;case"prophoto-rgb":i=w.ProPhoto_RGB;break;case"rec2020":i=w.Rec2020;break;case"lab":i=w.Lab;break;case"oklab":i=w.OKLab;break;case"xyz-d50":i=w.XYZ_D50;break;case"xyz":case"xyz-d65":i=w.XYZ_D65}a=An(n,i).channels,o=An(r,i).channels,a[0]=ee(a[0],o[0]),o[0]=ee(o[0],a[0]),a[1]=ee(a[1],o[1]),o[1]=ee(o[1],a[1]),a[2]=ee(a[2],o[2]),o[2]=ee(o[2],a[2]),a[0]=ke(a[0],l),a[1]=ke(a[1],l),a[2]=ke(a[2],l),o[0]=ke(o[0],u),o[1]=ke(o[1],u),o[2]=ke(o[2],u);const c=ie(l,u,s),f={colorNotation:i,channels:[on(ie(a[0],o[0],s),c),on(ie(a[1],o[1],s),c),on(ie(a[2],o[2],s),c)],alpha:c*e.alphaMultiplier,syntaxFlags:new Set([x.ColorMix])};return(e.a.color.syntaxFlags.has(x.Experimental)||e.b.color.syntaxFlags.has(x.Experimental))&&f.syntaxFlags.add(x.Experimental),f}function Ao(t,e,n){if(!n)return!1;const r=n.a.color,s=n.b.color,a=n.a.percentage/100;let o=r.channels,i=s.channels,l=0,u=0,c=0,f=0,m=0,d=0,$=w.RGB,N=r.alpha;if(typeof N!="number")return!1;let b=s.alpha;if(typeof b!="number")return!1;switch(N=Number.isNaN(N)?b:N,b=Number.isNaN(b)?N:b,t){case"hsl":$=w.HSL;break;case"hwb":$=w.HWB;break;case"lch":$=w.LCH;break;case"oklch":$=w.OKLCH}switch(o=An(r,$).channels,i=An(s,$).channels,t){case"hsl":case"hwb":l=o[0],u=i[0],c=o[1],f=i[1],m=o[2],d=i[2];break;case"lch":case"oklch":c=o[0],f=i[0],m=o[1],d=i[1],l=o[2],u=i[2]}l=ee(l,u),Number.isNaN(l)&&(l=0),u=ee(u,l),Number.isNaN(u)&&(u=0),c=ee(c,f),f=ee(f,c),m=ee(m,d),d=ee(d,m);const p=u-l;switch(e){case"shorter":p>180?l+=360:p<-180&&(u+=360);break;case"longer":-180<p&&p<180&&(p>0?l+=360:u+=360);break;case"increasing":p<0&&(u+=360);break;case"decreasing":p>0&&(l+=360);break;default:throw new Error("Unknown hue interpolation method")}c=ke(c,N),m=ke(m,N),f=ke(f,b),d=ke(d,b);let y=[0,0,0];const E=ie(N,b,a);switch(t){case"hsl":case"hwb":y=[ie(l,u,a),on(ie(c,f,a),E),on(ie(m,d,a),E)];break;case"lch":case"oklch":y=[on(ie(c,f,a),E),on(ie(m,d,a),E),ie(l,u,a)]}const C={colorNotation:$,channels:y,alpha:E*n.alphaMultiplier,syntaxFlags:new Set([x.ColorMix])};return(n.a.color.syntaxFlags.has(x.Experimental)||n.b.color.syntaxFlags.has(x.Experimental))&&C.syntaxFlags.add(x.Experimental),C}function ee(t,e){return Number.isNaN(t)?e:t}function ie(t,e,n){return t*n+e*(1-n)}function ke(t,e){return Number.isNaN(e)?t:Number.isNaN(t)?NaN:t*e}function on(t,e){return e===0||Number.isNaN(e)?t:Number.isNaN(t)?NaN:t/e}function Tc(t){const e=rt(t[4].value);if(e.match(/[^a-f0-9]/))return!1;const n={colorNotation:w.HEX,channels:[0,0,0],alpha:1,syntaxFlags:new Set([x.Hex])},r=e.length;if(r===3){const s=e[0],a=e[1],o=e[2];return n.channels=[parseInt(s+s,16)/255,parseInt(a+a,16)/255,parseInt(o+o,16)/255],n}if(r===6){const s=e[0]+e[1],a=e[2]+e[3],o=e[4]+e[5];return n.channels=[parseInt(s,16)/255,parseInt(a,16)/255,parseInt(o,16)/255],n}if(r===4){const s=e[0],a=e[1],o=e[2],i=e[3];return n.channels=[parseInt(s+s,16)/255,parseInt(a+a,16)/255,parseInt(o+o,16)/255],n.alpha=parseInt(i+i,16)/255,n.syntaxFlags.add(x.HasAlpha),n}if(r===8){const s=e[0]+e[1],a=e[2]+e[3],o=e[4]+e[5],i=e[6]+e[7];return n.channels=[parseInt(s,16)/255,parseInt(a,16)/255,parseInt(o,16)/255],n.alpha=parseInt(i,16)/255,n.syntaxFlags.add(x.HasAlpha),n}return!1}function ur(t){if(T(t))return t[4].value=t[4].value%360,t[1]=t[4].value.toString(),t;if(et(t)){let e=t[4].value;switch(rt(t[4].unit)){case"deg":break;case"rad":e=180*t[4].value/Math.PI;break;case"grad":e=.9*t[4].value;break;case"turn":e=360*t[4].value;break;default:return!1}return e%=360,[v.Number,e.toString(),t[2],t[3],{value:e,type:F.Number}]}return!1}function Lc(t,e,n){if(e===0){const r=ur(t);return r!==!1&&(et(t)&&n.syntaxFlags.add(x.HasDimensionValues),r)}if(Q(t)){e===3?n.syntaxFlags.add(x.HasPercentageAlpha):n.syntaxFlags.add(x.HasPercentageValues);let r=z(t[4].value,1,0,100);return e===3&&(r=z(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){if(e!==3)return!1;let r=z(t[4].value,1,0,100);return e===3&&(r=z(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}function Ic(t,e,n){if(ct(t)&&rt(t[4].value)==="none")return n.syntaxFlags.add(x.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:NaN,type:F.Number}];if(e===0){const r=ur(t);return r!==!1&&(et(t)&&n.syntaxFlags.add(x.HasDimensionValues),r)}if(Q(t)){e===3?n.syntaxFlags.add(x.HasPercentageAlpha):n.syntaxFlags.add(x.HasPercentageValues);let r=t[4].value;return e===3?r=z(t[4].value,100,0,1):e===1&&(r=z(t[4].value,1,0,2147483647)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){e!==3&&n.syntaxFlags.add(x.HasNumberValues);let r=t[4].value;return e===3?r=z(t[4].value,1,0,1):e===1&&(r=z(t[4].value,1,0,2147483647)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}function Mo(t,e,n,r){const s=[],a=[],o=[],i=[],l={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let u=s;for(let $=0;$<t.value.length;$++){let N=t.value[$];if(!Yt(N)&&!Jt(N)){if(R(N)&&Xt(N.value)){if(u===s){u=a;continue}if(u===a){u=o;continue}if(u===o){u=i;continue}if(u===i)return!1}if(Zt(N)){if(u===i&&N.getName().toLowerCase()==="var"){l.syntaxFlags.add(x.HasVariableAlpha),u.push(N);continue}if(!Lr.has(N.getName().toLowerCase()))return!1;const[[b]]=Qn([[N]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!b||!R(b)||!ut(b.value))return!1;Number.isNaN(b.value[4].value)&&(b.value[4].value=0),N=b}if(!R(N))return!1;u.push(N)}}if(u.length!==1||s.length!==1||a.length!==1||o.length!==1||!R(s[0])||!R(a[0])||!R(o[0]))return!1;const c=e(s[0].value,0,l);if(!c||!T(c))return!1;const f=e(a[0].value,1,l);if(!f||!T(f))return!1;const m=e(o[0].value,2,l);if(!m||!T(m))return!1;const d=[c,f,m];if(i.length===1)if(l.syntaxFlags.add(x.HasAlpha),R(i[0])){const $=e(i[0].value,3,l);if(!$||!T($))return!1;d.push($)}else l.alpha=i[0];return l.channels=[d[0][4].value,d[1][4].value,d[2][4].value],d.length===4&&(l.alpha=d[3][4].value),l}function ln(t,e,n,r,s){const a=[],o=[],i=[],l=[];let u,c,f=!1;const m={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let d=a;for(let y=0;y<t.value.length;y++){let E=t.value[y];if(Yt(E)||Jt(E))for(;Yt(t.value[y+1])||Jt(t.value[y+1]);)y++;else if(d===a&&a.length&&(d=o),d===o&&o.length&&(d=i),R(E)&&Pr(E.value)&&E.value[4].value==="/"){if(d===l)return!1;d=l}else{if(Zt(E)){if(d===l&&E.getName().toLowerCase()==="var"){m.syntaxFlags.add(x.HasVariableAlpha),d.push(E);continue}if(!Lr.has(E.getName().toLowerCase()))return!1;const[[C]]=Qn([[E]],{censorIntoStandardRepresentableValues:!0,globals:c,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!C||!R(C)||!ut(C.value))return!1;Number.isNaN(C.value[4].value)&&(C.value[4].value=0),E=C}if(d===a&&a.length===0&&R(E)&&ct(E.value)&&E.value[4].value.toLowerCase()==="from"){if(f)return!1;for(;Yt(t.value[y+1])||Jt(t.value[y+1]);)y++;if(y++,E=t.value[y],f=s(E),f===!1)return!1;f.syntaxFlags.has(x.Experimental)&&m.syntaxFlags.add(x.Experimental),m.syntaxFlags.add(x.RelativeColorSyntax),f.colorNotation!==n&&(f=An(f,n)),u=So(f),c=xo(u)}else{if(!R(E))return!1;if(ct(E.value)&&u){const C=E.value[4].value.toLowerCase();if(u.has(C)){d.push(new _(u.get(C)));continue}}d.push(E)}}}if(d.length!==1||a.length!==1||o.length!==1||i.length!==1||!R(a[0])||!R(o[0])||!R(i[0])||u&&!u.has("alpha"))return!1;const $=e(a[0].value,0,m);if(!$||!T($))return!1;const N=e(o[0].value,1,m);if(!N||!T(N))return!1;const b=e(i[0].value,2,m);if(!b||!T(b))return!1;const p=[$,N,b];if(l.length===1)if(m.syntaxFlags.add(x.HasAlpha),R(l[0])){const y=e(l[0].value,3,m);if(!y||!T(y))return!1;p.push(y)}else m.alpha=l[0];else if(u&&u.has("alpha")){const y=e(u.get("alpha"),3,m);if(!y||!T(y))return!1;p.push(y)}return m.channels=[p[0][4].value,p[1][4].value,p[2][4].value],p.length===4&&(m.alpha=p[3][4].value),m}function _c(t,e){if(t.value.some(n=>R(n)&&Xt(n.value))){const n=Hc(t);if(n!==!1)return n}{const n=zc(t,e);if(n!==!1)return n}return!1}function Hc(t){return Mo(t,Lc,w.HSL,[x.LegacyHSL])}function zc(t,e){return ln(t,Ic,w.HSL,[],e)}function Uc(t,e,n){if(ct(t)&&rt(t[4].value)==="none")return n.syntaxFlags.add(x.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:NaN,type:F.Number}];if(e===0){const r=ur(t);return r!==!1&&(et(t)&&n.syntaxFlags.add(x.HasDimensionValues),r)}if(Q(t)){e===3?n.syntaxFlags.add(x.HasPercentageAlpha):n.syntaxFlags.add(x.HasPercentageValues);let r=t[4].value;return e===3&&(r=z(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){e!==3&&n.syntaxFlags.add(x.HasNumberValues);let r=t[4].value;return e===3&&(r=z(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}function Gc(t,e,n){if(ct(t)&&rt(t[4].value)==="none")return n.syntaxFlags.add(x.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:NaN,type:F.Number}];if(Q(t)){e!==3&&n.syntaxFlags.add(x.HasPercentageValues);let r=z(t[4].value,1,0,100);return e===1||e===2?r=z(t[4].value,.8,-2147483647,2147483647):e===3&&(r=z(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){e!==3&&n.syntaxFlags.add(x.HasNumberValues);let r=z(t[4].value,1,0,100);return e===1||e===2?r=z(t[4].value,1,-2147483647,2147483647):e===3&&(r=z(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}function jc(t,e){return ln(t,Gc,w.Lab,[],e)}function qc(t,e,n){if(ct(t)&&rt(t[4].value)==="none")return n.syntaxFlags.add(x.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:NaN,type:F.Number}];if(e===2){const r=ur(t);return r!==!1&&(et(t)&&n.syntaxFlags.add(x.HasDimensionValues),r)}if(Q(t)){e!==3&&n.syntaxFlags.add(x.HasPercentageValues);let r=z(t[4].value,1,0,100);return e===1?r=z(t[4].value,100/150,0,2147483647):e===3&&(r=z(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){e!==3&&n.syntaxFlags.add(x.HasNumberValues);let r=z(t[4].value,1,0,100);return e===1?r=z(t[4].value,1,0,2147483647):e===3&&(r=z(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}function Vc(t,e){return ln(t,qc,w.LCH,[],e)}const Po=new Map;for(const[t,e]of Object.entries(Sc))Po.set(t,e);function Xc(t){const e=Po.get(rt(t));return!!e&&{colorNotation:w.RGB,channels:[e[0]/255,e[1]/255,e[2]/255],alpha:1,syntaxFlags:new Set([x.ColorKeyword,x.NamedColor])}}function Kc(t,e,n){if(ct(t)&&rt(t[4].value)==="none")return n.syntaxFlags.add(x.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:NaN,type:F.Number}];if(Q(t)){e!==3&&n.syntaxFlags.add(x.HasPercentageValues);let r=z(t[4].value,100,0,1);return e===1||e===2?r=z(t[4].value,250,-2147483647,2147483647):e===3&&(r=z(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){e!==3&&n.syntaxFlags.add(x.HasNumberValues);let r=z(t[4].value,1,0,1);return e===1||e===2?r=z(t[4].value,1,-2147483647,2147483647):e===3&&(r=z(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}function Zc(t,e){return ln(t,Kc,w.OKLab,[],e)}function Yc(t,e,n){if(ct(t)&&rt(t[4].value)==="none")return n.syntaxFlags.add(x.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:NaN,type:F.Number}];if(e===2){const r=ur(t);return r!==!1&&(et(t)&&n.syntaxFlags.add(x.HasDimensionValues),r)}if(Q(t)){e!==3&&n.syntaxFlags.add(x.HasPercentageValues);let r=z(t[4].value,100,0,1);return e===1?r=z(t[4].value,250,0,2147483647):e===3&&(r=z(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){e!==3&&n.syntaxFlags.add(x.HasNumberValues);let r=z(t[4].value,1,0,1);return e===1?r=z(t[4].value,1,0,2147483647):e===3&&(r=z(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}function Jc(t,e){return ln(t,Yc,w.OKLCH,[],e)}function Qc(t,e,n){if(Q(t)){e===3?n.syntaxFlags.add(x.HasPercentageAlpha):n.syntaxFlags.add(x.HasPercentageValues);const r=z(t[4].value,100,0,1);return[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){e!==3&&n.syntaxFlags.add(x.HasNumberValues);let r=z(t[4].value,255,0,1);return e===3&&(r=z(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}function t0(t,e,n){if(ct(t)&&t[4].value.toLowerCase()==="none")return n.syntaxFlags.add(x.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:NaN,type:F.Number}];if(Q(t)){e!==3&&n.syntaxFlags.add(x.HasPercentageValues);let r=z(t[4].value,100,-2147483647,2147483647);return e===3&&(r=z(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}if(T(t)){e!==3&&n.syntaxFlags.add(x.HasNumberValues);let r=z(t[4].value,255,-2147483647,2147483647);return e===3&&(r=z(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:F.Number}]}return!1}function e0(t,e){if(t.value.some(n=>R(n)&&Xt(n.value))){const n=n0(t);if(n!==!1)return(!n.syntaxFlags.has(x.HasNumberValues)||!n.syntaxFlags.has(x.HasPercentageValues))&&n}else{const n=r0(t,e);if(n!==!1)return n}return!1}function n0(t){return Mo(t,Qc,w.RGB,[x.LegacyRGB])}function r0(t,e){return ln(t,t0,w.RGB,[],e)}function s0(t){const e=_s(t);if(Cc(e))return No(e);let n=t;return n=$o(n),n[0]<1e-6&&(n=[0,0,0]),n[0]>.999999&&(n=[1,0,0]),ar(kc(n,a0,o0))}function a0(t){return t=mo(t),t=As(t),sr(t)}function o0(t){return t=or(t),t=Ps(t),vo(t)}function i0(t,e){let n=!1,r=!1;for(let i=0;i<t.value.length;i++){const l=t.value[i];if(!Yt(l)&&!Jt(l)&&(n||(n=e(l),!n))){if(!n||r||!R(l)||!ct(l.value)||rt(l.value[4].value)!=="max")return!1;r=!0}}if(!n||!r)return!1;n.channels=Et(n.channels),n.channels=s0(ko(n).channels),n.colorNotation=w.sRGB;const s={colorNotation:w.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([x.ContrastColor,x.Experimental])},a=Co(n.channels,[1,1,1]),o=Co(n.channels,[0,0,0]);return s.channels=a>o?[1,1,1]:[0,0,0],s}function le(t){if(Zt(t))switch(rt(t.getName())){case"rgb":case"rgba":return e0(t,le);case"hsl":case"hsla":return _c(t,le);case"hwb":return e=le,ln(t,Uc,w.HWB,[],e);case"lab":return jc(t,le);case"lch":return Vc(t,le);case"oklab":return Zc(t,le);case"oklch":return Jc(t,le);case"color":return Pc(t,le);case"color-mix":return Rc(t,le);case"contrast-color":return i0(t,le)}var e;if(R(t)){if(Wi(t.value))return Tc(t.value);if(ct(t.value)){const n=Xc(t.value[4].value);return n!==!1?n:rt(t.value[4].value)==="transparent"&&{colorNotation:w.RGB,channels:[0,0,0],alpha:0,syntaxFlags:new Set([x.ColorKeyword])}}}return!1}const{CloseParen:Do,Comment:Bo,Dimension:l0,EOF:Oo,Function:Ro,Ident:u0,Number:c0,OpenParen:Wo,Percentage:h0,Whitespace:To}=v,{HasNoneKeywords:Us}=x,f0=8,Mn=10,Gs=16,p0=100,js=255,Lo=new RegExp(`^${nr}(${en}|${Gr})\\s+`),d0=/(?:hsla?|hwb)$/,g0=new RegExp(`^(?:${Za}|${ru})$`),m0=new RegExp(tr),v0=new RegExp(nr),b0=new RegExp(`^${su}`),w0=new RegExp(Ga),qs=new RegExp(`^${nr}`),_t=new Xn({max:4096});function Io(t,e={}){if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{colorSpace:n,format:r}=e,s=new Map([["color",["r","g","b","alpha"]],["hsl",["h","s","l","alpha"]],["hsla",["h","s","l","alpha"]],["hwb",["h","w","b","alpha"]],["lab",["l","a","b","alpha"]],["lch",["l","c","h","alpha"]],["oklab",["l","a","b","alpha"]],["oklch",["l","c","h","alpha"]],["rgb",["r","g","b","alpha"]],["rgba",["r","g","b","alpha"]]]).get(n),a=new Set,o=[[],[],[],[]];let i=0,l=0,u=!1;for(;t.length;){const f=t.shift();if(!Array.isArray(f))throw new TypeError(`${f} is not an array.`);const[m,d,,,$={}]=f,N=$?.value,b=o[i];switch(m){case l0:{let p=lo(f,e);p||(p=d),b.push(p);break}case Ro:{b.push(d),u=!0,l++,w0.test(d)&&a.add(l);break}case u0:{if(!s||!s.includes(d))return null;b.push(d),u||i++;break}case c0:{const p=N??parseFloat(d);b.push(p),u||i++;break}case Wo:{b.push(d),l++;break}case Do:{u&&(b[b.length-1]===" "?b.splice(-1,1,d):b.push(d),a.has(l)&&a.delete(l),l--,l===0&&(u=!1,i++));break}case h0:{const p=N??parseFloat(d);b.push(p/p0),u||i++;break}case To:{if(b.length&&u){const p=b[b.length-1];(typeof p=="number"||V(p)&&!p.endsWith("(")&&p!==" ")&&b.push(d)}break}default:m!==Bo&&m!==Oo&&u&&b.push(d)}}const c=[];for(const f of o)if(f.length===1){const[m]=f;c.push(m)}else if(f.length){const m=mu(f.join(""),{format:r});if(m)c.push(m);else return null}return c}function y0(t,e={}){if(V(t)){if(t=t.toLowerCase().trim(),!t)return null;if(!qs.test(t))return t}else return null;const{currentColor:n,format:r}=e,s=`{preProcess:${t},opt:${Ct(e)}}`;if(_t.has(s))return _t.get(s);if(/currentcolor/.test(t))if(n)t=t.replace(/currentcolor/g,n);else return s&&_t.set(s,null),null;const a=t.match(b0);let o;if(a)[,o]=a;else return null;if(e.colorSpace=o,Lo.test(t)){const[,i]=t.match(Lo),[,l]=t.split(i);if(/^[a-z]+$/.test(i)){if(!/^transparent$/.test(i)&&!Object.prototype.hasOwnProperty.call(Ln,i))return s&&_t.set(s,null),null}else if(r===X){const u=Dn(i,e);t=t.replace(i,u)}if(r===X){const u=Je({css:l}),c=Io(u,e);if(!Array.isArray(c))return s&&_t.set(s,null),null;let f;if(c.length===3)f=` ${c.join(" ")})`;else{const[m,d,$,N]=c;f=` ${m} ${d} ${$} / ${N})`}t=t.replace(l,f)}}else{const[,i]=t.split(qs);if(qs.test(i)){const l=Je({css:i}),u=[];let c=0;for(;l.length;){const $=l.shift(),[N,b]=$;switch(N){case Ro:case Wo:{u.push(b),c++;break}case Do:{u[u.length-1]===" "?u.splice(-1,1,b):u.push(b),c--;break}case To:{const p=u[u.length-1];!p.endsWith("(")&&p!==" "&&u.push(b);break}default:N!==Bo&&N!==Oo&&u.push(b)}if(c===0)break}const f=Vs(u.join("").trim(),e);if(!f)return s&&_t.set(s,null),null;const m=Io(l,e);if(!Array.isArray(m))return s&&_t.set(s,null),null;let d;if(m.length===3)d=` ${m.join(" ")})`;else{const[$,N,b,p]=m;d=` ${$} ${N} ${b} / ${p})`}t=t.replace(i,`${f}${d}`)}}return s&&_t.set(s,t),t}function Vs(t,e={}){const{format:n}=e;if(V(t)){if(m0.test(t)){if(n===X)return t;throw new SyntaxError(`Unexpected token ${Hr} found.`)}else if(!v0.test(t))return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string`);const r=`{relativeColor:${t},opt:${Ct(e)}}`;if(_t.has(r))return _t.get(r);const s=y0(t,e);if(s)t=s;else return r&&_t.set(r,null),null;if(n===X)return t.startsWith("rgba(")?t=t.replace(/^rgba\(/,"rgb("):t.startsWith("hsla(")&&(t=t.replace(/^hsla\(/,"hsl(")),t;const a=Je({css:t}),o=Ui(a),i=le(o);if(!i)return r&&_t.set(r,null),null;const{alpha:l,channels:u,colorNotation:c,syntaxFlags:f}=i;let m;Number.isNaN(Number(l))?f instanceof Set&&f.has(Us)?m=g:m=0:m=A(l,f0);let d,$,N;[d,$,N]=u;let b;if(g0.test(c)){const p=f instanceof Set&&f.has(Us);Number.isNaN(d)?p?d=g:d=0:d=A(d,Gs),Number.isNaN($)?p?$=g:$=0:$=A($,Gs),Number.isNaN(N)?p?N=g:N=0:N=A(N,Gs),m===1?b=`${c}(${d} ${$} ${N})`:b=`${c}(${d} ${$} ${N} / ${m})`}else if(d0.test(c)){Number.isNaN(d)&&(d=0),Number.isNaN($)&&($=0),Number.isNaN(N)&&(N=0);let[p,y,E]=ai(`${c}(${d} ${$} ${N} / ${m})`);p=A(p/js,Mn),y=A(y/js,Mn),E=A(E/js,Mn),m===1?b=`color(srgb ${p} ${y} ${E})`:b=`color(srgb ${p} ${y} ${E} / ${m})`}else{const p=c==="rgb"?"srgb":c,y=f instanceof Set&&f.has(Us);Number.isNaN(d)?y?d=g:d=0:d=A(d,Mn),Number.isNaN($)?y?$=g:$=0:$=A($,Mn),Number.isNaN(N)?y?N=g:N=0:N=A(N,Mn),m===1?b=`color(${p} ${d} ${$} ${N})`:b=`color(${p} ${d} ${$} ${N} / ${m})`}return r&&_t.set(r,b),b}const Pn="rgba(0, 0, 0, 0)",$0=new RegExp(Ir),N0=new RegExp(to),_o=new RegExp(tr),gt=new Xn({max:4096}),Dn=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{currentColor:n,customProperty:r={},format:s=at,key:a}=e;let o;if((!_o.test(t)||typeof r.callback=="function")&&(o=`{resolve:${t},opt:${Ct(e)}}`,gt.has(o)))return gt.get(o);let i,l,u,c,f,m;if(_o.test(t)){if(s===X)return o&&gt.set(o,t),t;const d=Ss(t,e);if(d)t=d;else switch(s){case"hex":case"hexAlpha":return o&&gt.set(o,null),null;default:return i=Pn,o&&gt.set(o,i),i}}if(e.format!==s&&(e.format=s),t=t.toLowerCase(),N0.test(t)){const d=Vs(t,e);if(s===at)return d?i=d:i=Pn,o&&gt.set(o,i),i;if(s===X)return d?i=d:i="",o&&gt.set(o,i),i;d?t=d:t=""}if($0.test(t)){const d=rr(t,e);d?t=d:t=""}if(t==="transparent")switch(s){case X:return o&&gt.set(o,t),t;case"hex":return o&&gt.set(o,null),null;case"hexAlpha":return i="#00000000",o&&gt.set(o,i),i;case at:default:return i=Pn,o&&gt.set(o,i),i}else if(t==="currentcolor"){if(s===X)return o&&gt.set(o,t),t;if(n)n.startsWith(Ge)?[l,u,c,f,m]=_n(n,e):n.startsWith(ot)?[l,u,c,f,m]=We(n,e):[l,u,c,f,m]=cn(n,e);else if(s===at)return i=Pn,o&&gt.set(o,i),i}else if(s===X){if(t.startsWith(Ge))return i=_n(t,e),o&&gt.set(o,i),i;if(t.startsWith(ot))return[l,u,c,f,m]=We(t,e),m===1?i=`color(${l} ${u} ${c} ${f})`:i=`color(${l} ${u} ${c} ${f} / ${m})`,o&&gt.set(o,i),i;{const d=cn(t,e);return d?([l,u,c,f,m]=d,l==="rgb"?(m===1?i=`${l}(${u}, ${c}, ${f})`:i=`${l}a(${u}, ${c}, ${f}, ${m})`,o&&gt.set(o,i),i):(m===1?i=`${l}(${u} ${c} ${f})`:i=`${l}(${u} ${c} ${f} / ${m})`,o&&gt.set(o,i),i)):(i="",o&&gt.set(o,i),i)}}else/currentcolor/.test(t)?(n&&(t=t.replace(/currentcolor/g,n)),/transparent/.test(t)&&(t=t.replace(/transparent/g,Pn)),t.startsWith(Ge)&&([l,u,c,f,m]=_n(t,e))):/transparent/.test(t)?(t=t.replace(/transparent/g,Pn),t.startsWith(Ge)&&([l,u,c,f,m]=_n(t,e))):t.startsWith(Ge)?[l,u,c,f,m]=_n(t,e):t.startsWith(ot)?[l,u,c,f,m]=We(t,e):t&&([l,u,c,f,m]=cn(t,e));switch(s){case"hex":{let d;isNaN(u)||isNaN(c)||isNaN(f)||isNaN(m)||m===0?d=null:d=Yo([u,c,f]),a?i=[a,d]:i=d;break}case"hexAlpha":{let d;isNaN(u)||isNaN(c)||isNaN(f)||isNaN(m)?d=null:d=Yo([u,c,f,m]),a?i=[a,d]:i=d;break}case at:default:{let d;switch(l){case"rgb":{m===1?d=`${l}(${u}, ${c}, ${f})`:d=`${l}a(${u}, ${c}, ${f}, ${m})`;break}case"lab":case"lch":case"oklab":case"oklch":{m===1?d=`${l}(${u} ${c} ${f})`:d=`${l}(${u} ${c} ${f} / ${m})`;break}default:m===1?d=`color(${l} ${u} ${c} ${f})`:d=`color(${l} ${u} ${c} ${f} / ${m})`}a?i=[a,d]:i=d}}return o&&gt.set(o,i),i},E0=10,Xs=16,Bn=360,Kr=180,C0=new RegExp(`^(?:${en})$`),k0=new RegExp(`${Gr}`),cr=t=>{if(V(t)&&(t=t.toLowerCase().trim(),t))if(/^[a-z]+$/.test(t)){if(/^(?:currentcolor|transparent)$/.test(t)||Object.prototype.hasOwnProperty.call(Ln,t))return!0}else return C0.test(t)||k0.test(t)?!0:!!Dn(t,{format:X});return!1},Ct=(t,e=!1)=>typeof t>"u"?"":JSON.stringify(t,(n,r)=>{let s;return typeof r>"u"?s=null:typeof r=="function"?e?s=r.toString():s=r.name:r instanceof Map||r instanceof Set?s=[...r]:typeof r=="bigint"?s=r.toString():s=r,s}),A=(t,e=0)=>{if(!Number.isFinite(t))throw new TypeError(`${t} is not a number.`);if(Number.isFinite(e)){if(e<0||e>Xs)throw new RangeError(`${e} is not between 0 and ${Xs}.`)}else throw new TypeError(`${e} is not a number.`);if(e===0)return Math.round(t);let n;return e===Xs?n=t.toPrecision(6):e<E0?n=t.toPrecision(4):n=t.toPrecision(5),parseFloat(n)},Ho=(t,e,n="shorter")=>{if(!Number.isFinite(t))throw new TypeError(`${t} is not a number.`);if(!Number.isFinite(e))throw new TypeError(`${e} is not a number.`);switch(n){case"decreasing":{e>t&&(t+=Bn);break}case"increasing":{e<t&&(e+=Bn);break}case"longer":{e>t&&e<t+Kr?t+=Bn:e>t+Kr*-1&&e<=t&&(e+=Bn);break}case"shorter":default:e>t+Kr?t+=Bn:e<t+Kr*-1&&(e+=Bn)}return[t,e]},U="mixValue",Ks=.001,ue=.5,Zs=2,At=3,ce=4,Fe=8,hr=10,On=12,P=16,F0=60,ft=360,H=100,W=255,un=2,fr=3,Rn=2.4,Wn=12.92,ne=.055,pr=116,zo=500,Uo=200,Ys=216/24389,Zr=24389/27,Go=[.3457/.3585,1,(1-.3457-.3585)/.3585],dr=[[.955473421488075,-.02309845494876471,.06325924320057072],[-.0283697093338637,1.0099953980813041,.021041441191917323],[.012314014864481998,-.020507649298898964,1.330365926242124]],he=[[1.0479297925449969,.022946870601609652,-.05019226628920524],[.02962780877005599,.9904344267538799,-.017073799063418826],[-.009243040646204504,.015055191490298152,.7518742814281371]],Js=[[506752/1228815,87881/245763,12673/70218],[87098/409605,175762/245763,12673/175545],[7918/409605,87881/737289,1001167/1053270]],Yr=[[12831/3959,-329/214,-1974/3959],[-851781/878810,1648619/878810,36519/878810],[705/12673,-2585/12673,705/667]],S0=[[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],jo=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],qo=[[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]],x0=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],A0=[[608311/1250200,189793/714400,198249/1000160],[35783/156275,247089/357200,198249/2500400],[0/1,32229/714400,5220557/5000800]],M0=[[63426534/99577255,20160776/139408157,47086771/278816314],[26158966/99577255,472592308/697040785,8267143/139408157],[0/1,19567812/697040785,295819943/278816314]],P0=[[573536/994567,263643/1420810,187206/994567],[591459/1989134,6239551/9945670,374412/4972835],[53769/1989134,351524/4972835,4929758/4972835]],D0=[[.7977666449006423,.13518129740053308,.0313477341283922],[.2880748288194013,.711835234241873,8993693872564e-17],[0,0,.8251046025104602]],Vo=new RegExp(`^(?:${en})$`),Jr=new RegExp(`^${nu}$`),B0=/^xyz(?:-d(?:50|65))?$/,pt=/^currentColor$/i,Tn=new RegExp(`^color\\(\\s*(${Qa})\\s*\\)$`),Qs=new RegExp(`^hsla?\\(\\s*(${ks}|${eo})\\s*\\)$`),ta=new RegExp(`^hwb\\(\\s*(${ks})\\s*\\)$`),ea=new RegExp(`^lab\\(\\s*(${zr})\\s*\\)$`),na=new RegExp(`^lch\\(\\s*(${Fs})\\s*\\)$`),Xo=new RegExp(`^${Gr}$`),O0=new RegExp(`^${au}$`),Ko=new RegExp(`${Gr}`,"g"),ra=new RegExp(`^oklab\\(\\s*(${zr})\\s*\\)$`),sa=new RegExp(`^oklch\\(\\s*(${Fs})\\s*\\)$`),J=/^(?:specifi|comput)edValue$/,Ln={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},re=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{alpha:n=!1,minLength:r=At,maxLength:s=ce,minRange:a=0,maxRange:o=1,validateRange:i=!0}=e;if(!Number.isFinite(r))throw new TypeError(`${r} is not a number.`);if(!Number.isFinite(s))throw new TypeError(`${s} is not a number.`);if(!Number.isFinite(a))throw new TypeError(`${a} is not a number.`);if(!Number.isFinite(o))throw new TypeError(`${o} is not a number.`);const l=t.length;if(l<r||l>s)throw new Error(`Unexpected array length ${l}.`);let u=0;for(;u<l;){const c=t[u];if(Number.isFinite(c)){if(u<At&&i&&(c<a||c>o))throw new RangeError(`${c} is not between ${a} and ${o}.`);if(u===At&&(c<0||c>1))throw new RangeError(`${c} is not between 0 and 1.`)}else throw new TypeError(`${c} is not a number.`);u++}return n&&l===At&&t.push(1),t},Z=(t,e,n=!1)=>{if(Array.isArray(t)){if(t.length!==At)throw new Error(`Unexpected array length ${t.length}.`);if(!n)for(let y of t)y=re(y,{maxLength:At,validateRange:!1})}else throw new TypeError(`${t} is not an array.`);const[[r,s,a],[o,i,l],[u,c,f]]=t;let m,d,$;n?[m,d,$]=e:[m,d,$]=re(e,{maxLength:At,validateRange:!1});const N=r*m+s*d+a*$,b=o*m+i*d+l*$,p=u*m+c*d+f*$;return[N,b,p]},gr=(t,e,n=!1)=>{if(Array.isArray(t)){if(t.length!==ce)throw new Error(`Unexpected array length ${t.length}.`)}else throw new TypeError(`${t} is not an array.`);if(Array.isArray(e)){if(e.length!==ce)throw new Error(`Unexpected array length ${e.length}.`)}else throw new TypeError(`${e} is not an array.`);let r=0;for(;r<ce;)t[r]===g&&e[r]===g?(t[r]=0,e[r]=0):t[r]===g?t[r]=e[r]:e[r]===g&&(e[r]=t[r]),r++;return n||(t=re(t,{minLength:ce,validateRange:!1}),e=re(e,{minLength:ce,validateRange:!1})),[t,e]},mr=t=>{if(Number.isFinite(t)){if(t=Math.round(t),t<0||t>W)throw new RangeError(`${t} is not between 0 and ${W}.`)}else throw new TypeError(`${t} is not a number.`);let e=t.toString(P);return e.length===1&&(e=`0${e}`),e},Qr=t=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const e=ft/400,n=ft/(Math.PI*Zs),r=new RegExp(`^(${te})(${Ns})?$`);if(!r.test(t))throw new SyntaxError(`Invalid property value: ${t}`);const[,s,a]=t.match(r),o=s[0]==="."?`0${s}`:s;let i;switch(a){case"grad":i=parseFloat(o)*e;break;case"rad":i=parseFloat(o)*n;break;case"turn":i=parseFloat(o)*ft;break;default:i=parseFloat(o)}return i%=ft,i<0?i+=ft:Object.is(i,-0)&&(i=0),i},Xe=t=>{let e=t;if(V(e))if(e=e.trim(),!e)e=1;else if(e===g)e=0;else{if(e[0]==="."&&(e=`0${e}`),e.endsWith("%")?e=parseFloat(e)/H:e=parseFloat(e),!Number.isFinite(e))throw new TypeError(`${e} is not a number.`);e<Ks?e=0:e>1?e=1:e=parseFloat(e.toFixed(3))}else e=1;return e},Zo=t=>{if(V(t)){if(t==="")throw new SyntaxError("Invalid property value: (empty string)");t=t.trim()}else throw new TypeError(`${t} is not a string.`);let e=parseInt(t,P);if(e<=0)return 0;if(e>=W)return 1;const n=new Map;for(let r=1;r<H;r++)n.set(Math.round(r*W/H),r);return n.has(e)?e=n.get(e)/H:e=Math.round(e/W/Ks)*Ks,parseFloat(e.toFixed(3))},aa=(t,e=!1)=>{let n,r,s;e?[n,r,s]=t:[n,r,s]=re(t,{maxLength:At,maxRange:W});let a=n/W,o=r/W,i=s/W;const l=.04045;return a>l?a=Math.pow((a+ne)/(1+ne),Rn):a/=Wn,o>l?o=Math.pow((o+ne)/(1+ne),Rn):o/=Wn,i>l?i=Math.pow((i+ne)/(1+ne),Rn):i/=Wn,[a,o,i]},oa=(t,e=!1)=>{let n,r,s,a;e?[n,r,s,a]=t:[n,r,s,a]=re(t,{alpha:!0,maxRange:W});const[o,i,l]=aa([n,r,s],!0),[u,c,f]=Z(Js,[o,i,l],!0);return[u,c,f,a]},Yo=t=>{const[e,n,r,s]=re(t,{alpha:!0,maxRange:W}),a=mr(e),o=mr(n),i=mr(r),l=mr(s*W);let u;return l==="ff"?u=`#${a}${o}${i}`:u=`#${a}${o}${i}${l}`,u},Jo=(t,e=!1)=>{let[n,r,s]=re(t,{maxLength:At});const a=809/258400;return n>a?n=Math.pow(n,1/Rn)*(1+ne)-ne:n*=Wn,n*=W,r>a?r=Math.pow(r,1/Rn)*(1+ne)-ne:r*=Wn,r*=W,s>a?s=Math.pow(s,1/Rn)*(1+ne)-ne:s*=Wn,s*=W,[e?Math.round(n):n,e?Math.round(r):r,e?Math.round(s):s]},In=(t,e=!1)=>{let n,r,s,a;e?[n,r,s,a]=t:[n,r,s,a]=re(t,{validateRange:!1});let[o,i,l]=Z(Yr,[n,r,s],!0);return[o,i,l]=Jo([Math.min(Math.max(o,0),1),Math.min(Math.max(i,0),1),Math.min(Math.max(l,0),1)],!0),[o,i,l,a]},Qo=(t,e=!1)=>{const[n,r,s,a]=In(t,e),o=n/W,i=r/W,l=s/W,u=Math.max(o,i,l),c=Math.min(o,i,l),f=u-c,m=(u+c)*ue*H;let d,$;if(Math.round(m)===0||Math.round(m)===H)d=g,$=g;else if($=f/(1-Math.abs(u+c-1))*H,$===0)d=g;else{switch(u){case o:d=(i-l)/f;break;case i:d=(l-o)/f+Zs;break;case l:default:d=(o-i)/f+ce;break}d=d*F0%ft,d<0&&(d+=ft)}return[d,$,m,a]},R0=(t,e=!1)=>{const[n,r,s,a]=In(t,e),o=Math.min(n,r,s)/W,i=1-Math.max(n,r,s)/W;let l;return o+i===1?l=g:[l]=Qo(t),[l,o*H,i*H,a]},ti=(t,e=!1)=>{let n,r,s,a;e?[n,r,s,a]=t:[n,r,s,a]=re(t,{validateRange:!1});const o=Z(S0,[n,r,s],!0).map(f=>Math.cbrt(f));let[i,l,u]=Z(x0,o,!0);i=Math.min(Math.max(i,0),1);const c=Math.round(parseFloat(i.toFixed(ce))*H);return(c===0||c===H)&&(l=g,u=g),[i,l,u,a]},W0=(t,e=!1)=>{const[n,r,s,a]=ti(t,e);let o,i;const l=Math.round(parseFloat(n.toFixed(ce))*H);return l===0||l===H?(o=g,i=g):(o=Math.max(Math.sqrt(Math.pow(r,un)+Math.pow(s,un)),0),parseFloat(o.toFixed(ce))===0?i=g:(i=Math.atan2(s,r)*ft*ue/Math.PI,i<0&&(i+=ft))),[n,o,i,a]},ei=(t,e=!1)=>{let n,r,s,a;e?[n,r,s,a]=t:[n,r,s,a]=re(t,{minLength:ce,validateRange:!1});const o=Z(dr,[n,r,s],!0),[i,l,u]=In(o,!0);return[i,l,u,a]},ni=(t,e=!1)=>{let n,r,s,a;e?[n,r,s,a]=t:[n,r,s,a]=re(t,{validateRange:!1});const o=[n,r,s].map((d,$)=>d/Go[$]),[i,l,u]=o.map(d=>d>Ys?Math.cbrt(d):(d*Zr+P)/pr),c=Math.min(Math.max(pr*l-P,0),H);let f,m;return c===0||c===H?(f=g,m=g):(f=(i-l)*zo,m=(l-u)*Uo),[c,f,m,a]},T0=(t,e=!1)=>{const[n,r,s,a]=ni(t,e);let o,i;return n===0||n===H?(o=g,i=g):(o=Math.max(Math.sqrt(Math.pow(r,un)+Math.pow(s,un)),0),i=Math.atan2(s,r)*ft*ue/Math.PI,i<0&&(i+=ft)),[n,o,i,a]},ia=t=>{if(V(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);if(!(/^#[\da-f]{6}$/.test(t)||/^#[\da-f]{3}$/.test(t)||/^#[\da-f]{8}$/.test(t)||/^#[\da-f]{4}$/.test(t)))throw new SyntaxError(`Invalid property value: ${t}`);const e=[];if(/^#[\da-f]{6}$/.test(t)){const[,n,r,s]=t.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);e.push(parseInt(n,P),parseInt(r,P),parseInt(s,P),1)}else if(/^#[\da-f]{3}$/.test(t)){const[,n,r,s]=t.match(/^#([\da-f])([\da-f])([\da-f])$/);e.push(parseInt(`${n}${n}`,P),parseInt(`${r}${r}`,P),parseInt(`${s}${s}`,P),1)}else if(/^#[\da-f]{8}$/.test(t)){const[,n,r,s,a]=t.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);e.push(parseInt(n,P),parseInt(r,P),parseInt(s,P),Zo(a))}else if(/^#[\da-f]{4}$/.test(t)){const[,n,r,s,a]=t.match(/^#([\da-f])([\da-f])([\da-f])([\da-f])$/);e.push(parseInt(`${n}${n}`,P),parseInt(`${r}${r}`,P),parseInt(`${s}${s}`,P),Zo(`${a}${a}`))}return e},L0=t=>{const[e,n,r,s]=ia(t),[a,o,i]=aa([e,n,r],!0);return[a,o,i,s]},I0=t=>{const[e,n,r,s]=L0(t),[a,o,i]=Z(Js,[e,n,r],!0);return[a,o,i,s]},ri=(t,e={})=>{if(V(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e,r=new RegExp(`^rgba?\\(\\s*(${zr}|${no})\\s*\\)$`);if(!r.test(t))switch(n){case U:return null;case X:return"";default:return["rgb",0,0,0,0]}const[,s]=t.match(r);let[a,o,i,l]=s.replace(/[,/]/g," ").split(/\s+/),u,c,f;a===g?u=0:(a[0]==="."&&(a=`0${a}`),a.endsWith("%")?u=parseFloat(a)*W/H:u=parseFloat(a),u=Math.min(Math.max(A(u,Fe),0),W)),o===g?c=0:(o[0]==="."&&(o=`0${o}`),o.endsWith("%")?c=parseFloat(o)*W/H:c=parseFloat(o),c=Math.min(Math.max(A(c,Fe),0),W)),i===g?f=0:(i[0]==="."&&(i=`0${i}`),i.endsWith("%")?f=parseFloat(i)*W/H:f=parseFloat(i),f=Math.min(Math.max(A(f,Fe),0),W));const m=Xe(l);return["rgb",u,c,f,n===U&&l===g?g:m]},ts=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;if(!Qs.test(t))switch(n){case"hsl":case U:return null;case X:return"";default:return["rgb",0,0,0,0]}const[,r]=t.match(Qs);let[s,a,o,i]=r.replace(/[,/]/g," ").split(/\s+/);if(s===g?n!=="hsl"&&(s=0):s=Qr(s),a===g?n!=="hsl"&&(a=0):(a[0]==="."&&(a=`0${a}`),a=Math.min(Math.max(parseFloat(a),0),H)),o===g?n!=="hsl"&&(o=0):(o[0]==="."&&(o=`0${o}`),o=Math.min(Math.max(parseFloat(o),0),H)),(i!==g||n!=="hsl")&&(i=Xe(i)),n==="hsl")return[n,s,a,o,i];const l=o/H,u=a/H*Math.min(l,1-l),c=s/ft*On%On,f=(8+s/ft*On)%On,m=(4+s/ft*On)%On,d=l-u*Math.max(-1,Math.min(c-At,At**un-c,1)),$=l-u*Math.max(-1,Math.min(f-At,At**un-f,1)),N=l-u*Math.max(-1,Math.min(m-At,At**un-m,1));return["rgb",Math.min(Math.max(A(d*W,Fe),0),W),Math.min(Math.max(A($*W,Fe),0),W),Math.min(Math.max(A(N*W,Fe),0),W),i]},la=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;if(!ta.test(t))switch(n){case"hwb":case U:return null;case X:return"";default:return["rgb",0,0,0,0]}const[,r]=t.match(ta);let[s,a,o,i]=r.replace("/"," ").split(/\s+/);if(s===g?n!=="hwb"&&(s=0):s=Qr(s),a===g?n!=="hwb"&&(a=0):(a[0]==="."&&(a=`0${a}`),a=Math.min(Math.max(parseFloat(a),0),H)/H),o===g?n!=="hwb"&&(o=0):(o[0]==="."&&(o=`0${o}`),o=Math.min(Math.max(parseFloat(o),0),H)/H),(i!==g||n!=="hwb")&&(i=Xe(i)),n==="hwb")return[n,s,a===g?a:a*H,o===g?o:o*H,i];if(a+o>=1){const m=A(a/(a+o)*W,Fe);return["rgb",m,m,m,i]}const l=(1-a-o)/W;let[,u,c,f]=ts(`hsl(${s} 100 50)`);return u=A((u*l+a)*W,Fe),c=A((c*l+a)*W,Fe),f=A((f*l+a)*W,Fe),["rgb",Math.min(Math.max(u,0),W),Math.min(Math.max(c,0),W),Math.min(Math.max(f,0),W),i]},vr=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;if(!ea.test(t))switch(n){case U:return null;case X:return"";default:return["rgb",0,0,0,0]}const r=1.25,s=8,[,a]=t.match(ea);let[o,i,l,u]=a.replace("/"," ").split(/\s+/);if(o===g?J.test(n)||(o=0):(o[0]==="."&&(o=`0${o}`),o.endsWith("%")?(o=parseFloat(o),o>H&&(o=H)):o=parseFloat(o),o<0&&(o=0)),i===g?J.test(n)||(i=0):(i[0]==="."&&(i=`0${i}`),i.endsWith("%")?i=parseFloat(i)*r:i=parseFloat(i)),l===g?J.test(n)||(l=0):l.endsWith("%")?l=parseFloat(l)*r:l=parseFloat(l),(u!==g||!J.test(n))&&(u=Xe(u)),J.test(n))return["lab",o===g?o:A(o,P),i===g?i:A(i,P),l===g?l:A(l,P),u];const c=(o+P)/pr,f=i/zo+c,m=c-l/Uo,d=Math.pow(c,fr),$=Math.pow(f,fr),N=Math.pow(m,fr),b=[$>Ys?$:(f*pr-P)/Zr,o>s?d:o/Zr,N>Ys?N:(m*pr-P)/Zr],[p,y,E]=b.map((C,S)=>C*Go[S]);return["xyz-d50",A(p,P),A(y,P),A(E,P),u]},es=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;if(!na.test(t))switch(n){case U:return null;case X:return"";default:return["rgb",0,0,0,0]}const r=1.5,[,s]=t.match(na);let[a,o,i,l]=s.replace("/"," ").split(/\s+/);if(a===g?J.test(n)||(a=0):(a[0]==="."&&(a=`0${a}`),a=parseFloat(a),a<0&&(a=0)),o===g?J.test(n)||(o=0):(o[0]==="."&&(o=`0${o}`),o.endsWith("%")?o=parseFloat(o)*r:o=parseFloat(o)),i===g?J.test(n)||(i=0):i=Qr(i),(l!==g||!J.test(n))&&(l=Xe(l)),J.test(n))return["lch",a===g?a:A(a,P),o===g?o:A(o,P),i===g?i:A(i,P),l];const u=o*Math.cos(i*Math.PI/(ft*ue)),c=o*Math.sin(i*Math.PI/(ft*ue)),[,f,m,d]=vr(`lab(${a} ${u} ${c})`);return["xyz-d50",A(f,P),A(m,P),A(d,P),l]},ns=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;if(!ra.test(t))switch(n){case U:return null;case X:return"";default:return["rgb",0,0,0,0]}const r=.4,[,s]=t.match(ra);let[a,o,i,l]=s.replace("/"," ").split(/\s+/);if(a===g?J.test(n)||(a=0):(a[0]==="."&&(a=`0${a}`),a.endsWith("%")?a=parseFloat(a)/H:a=parseFloat(a),a<0&&(a=0)),o===g?J.test(n)||(o=0):(o[0]==="."&&(o=`0${o}`),o.endsWith("%")?o=parseFloat(o)*r/H:o=parseFloat(o)),i===g?J.test(n)||(i=0):i.endsWith("%")?i=parseFloat(i)*r/H:i=parseFloat(i),(l!==g||!J.test(n))&&(l=Xe(l)),J.test(n))return["oklab",a===g?a:A(a,P),o===g?o:A(o,P),i===g?i:A(i,P),l];const u=Z(qo,[a,o,i]).map(d=>Math.pow(d,fr)),[c,f,m]=Z(jo,u,!0);return["xyz-d65",A(c,P),A(f,P),A(m,P),l]},rs=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;if(!sa.test(t))switch(n){case U:return null;case X:return"";default:return["rgb",0,0,0,0]}const r=.4,[,s]=t.match(sa);let[a,o,i,l]=s.replace("/"," ").split(/\s+/);if(a===g?J.test(n)||(a=0):(a[0]==="."&&(a=`0${a}`),a.endsWith("%")?a=parseFloat(a)/H:a=parseFloat(a),a<0&&(a=0)),o===g?J.test(n)||(o=0):(o[0]==="."&&(o=`0${o}`),o.endsWith("%")?o=parseFloat(o)*r/H:o=parseFloat(o),o<0&&(o=0)),i===g?J.test(n)||(i=0):i=Qr(i),(l!==g||!J.test(n))&&(l=Xe(l)),J.test(n))return["oklch",a===g?a:A(a,P),o===g?o:A(o,P),i===g?i:A(i,P),l];const u=o*Math.cos(i*Math.PI/(ft*ue)),c=o*Math.sin(i*Math.PI/(ft*ue)),f=Z(qo,[a,u,c]).map(N=>Math.pow(N,fr)),[m,d,$]=Z(jo,f,!0);return["xyz-d65",A(m,P),A(d,P),A($,P),l]},ht=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n,d50:r,format:s}=e;if(!Tn.test(t))switch(s){case U:return null;case X:return"";default:return["rgb",0,0,0,0]}const[,a]=t.match(Tn);let[o,i,l,u,c]=a.replace("/"," ").split(/\s+/),f,m,d;o==="xyz"&&(o="xyz-d65"),i===g?f=0:(i[0]==="."&&(i=`0${i}`),f=i.endsWith("%")?parseFloat(i)/H:parseFloat(i)),l===g?m=0:(l[0]==="."&&(l=`0${l}`),m=l.endsWith("%")?parseFloat(l)/H:parseFloat(l)),u===g?d=0:(u[0]==="."&&(u=`0${u}`),d=u.endsWith("%")?parseFloat(u)/H:parseFloat(u));const $=Xe(c);if(J.test(s)||s===U&&o===n)return[o,i===g?g:A(f,hr),l===g?g:A(m,hr),u===g?g:A(d,hr),c===g?g:$];let N,b,p;if(o==="srgb")[N,b,p]=oa([f*W,m*W,d*W]),r&&([N,b,p]=Z(he,[N,b,p],!0));else if(o==="srgb-linear")[N,b,p]=Z(Js,[f,m,d]),r&&([N,b,p]=Z(he,[N,b,p],!0));else if(o==="display-p3"){const y=aa([f*W,m*W,d*W]);[N,b,p]=Z(A0,y),r&&([N,b,p]=Z(he,[N,b,p],!0))}else if(o==="rec2020"){const y=1.09929682680944,E=.018053968510807,C=.45,S=[f,m,d].map(O=>{let D;return O<E*C*hr?D=O/(C*hr):D=Math.pow((O+y-1)/y,1/C),D});[N,b,p]=Z(M0,S),r&&([N,b,p]=Z(he,[N,b,p],!0))}else if(o==="a98-rgb"){const y=2.19921875,E=[f,m,d].map(C=>Math.pow(C,y));[N,b,p]=Z(P0,E),r&&([N,b,p]=Z(he,[N,b,p],!0))}else if(o==="prophoto-rgb"){const y=[f,m,d].map(E=>{let C;return E>1/(P*Zs)?C=Math.pow(E,1.8):C=E/P,C});[N,b,p]=Z(D0,y),r||([N,b,p]=Z(dr,[N,b,p],!0))}else/^xyz(?:-d(?:50|65))?$/.test(o)&&([N,b,p]=[f,m,d],o==="xyz-d50"?r||([N,b,p]=Z(dr,[N,b,p])):r&&([N,b,p]=Z(he,[N,b,p],!0)));return[r?"xyz-d50":"xyz-d65",A(N,P),A(b,P),A(p,P),s===U&&c===g?g:$]},mt=(t,e={})=>{if(V(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{d50:n,format:r}=e;if(!Vo.test(t))switch(r){case U:return null;case X:return"";default:return["rgb",0,0,0,0]}let s,a,o,i;if(pt.test(t)){if(r===at)return["rgb",0,0,0,0];if(r===X)return t;s=0,a=0,o=0,i=0}else if(/^[a-z]+$/.test(t))if(Object.prototype.hasOwnProperty.call(Ln,t)){if(r===X)return t;const[l,u,c]=Ln[t];if(i=1,r===at)return["rgb",l,u,c,i];[s,a,o]=oa([l,u,c],!0),n&&([s,a,o]=Z(he,[s,a,o],!0))}else{if(r===at)return["rgb",0,0,0,0];if(r===X)return t==="transparent"?t:"";if(r===U)return t==="transparent"?["rgb",0,0,0,0]:null;s=0,a=0,o=0,i=0}else if(t[0]==="#"){if(J.test(r))return["rgb",...ia(t)];[s,a,o,i]=I0(t),n&&([s,a,o]=Z(he,[s,a,o],!0))}else if(t.startsWith("lab")){if(J.test(r))return vr(t,e);[,s,a,o,i]=vr(t),n||([s,a,o]=Z(dr,[s,a,o],!0))}else if(t.startsWith("lch")){if(J.test(r))return es(t,e);[,s,a,o,i]=es(t),n||([s,a,o]=Z(dr,[s,a,o],!0))}else if(t.startsWith("oklab")){if(J.test(r))return ns(t,e);[,s,a,o,i]=ns(t),n&&([s,a,o]=Z(he,[s,a,o],!0))}else if(t.startsWith("oklch")){if(J.test(r))return rs(t,e);[,s,a,o,i]=rs(t),n&&([s,a,o]=Z(he,[s,a,o],!0))}else{let l,u,c;if(t.startsWith("hsl")?[,l,u,c,i]=ts(t):t.startsWith("hwb")?[,l,u,c,i]=la(t):[,l,u,c,i]=ri(t,e),J.test(r))return["rgb",Math.round(l),Math.round(u),Math.round(c),i];[s,a,o]=oa([l,u,c]),n&&([s,a,o]=Z(he,[s,a,o],!0))}return[n?"xyz-d50":"xyz-d65",A(s,P),A(a,P),A(o,P),i]},cn=(t,e={})=>{if(V(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n,format:r}=e;if(!Vo.test(t))switch(r){case U:return null;case X:return"";default:return["rgb",0,0,0,0]}let s,a,o,i,l;if(pt.test(t)){if(r===X)return t;a=0,o=0,i=0,l=0}else if(/^[a-z]+$/.test(t))if(Object.prototype.hasOwnProperty.call(Ln,t)){if(r===X)return t;[a,o,i]=Ln[t],l=1}else{if(r===X)return t==="transparent"?t:"";if(r===U)return t==="transparent"?["rgb",0,0,0,0]:null;a=0,o=0,i=0,l=0}else if(t[0]==="#")[a,o,i,l]=ia(t);else if(t.startsWith("rgb"))[,a,o,i,l]=ri(t,e);else if(t.startsWith("hsl"))[,a,o,i,l]=ts(t,e);else if(t.startsWith("hwb"))[,a,o,i,l]=la(t,e);else if(/^l(?:ab|ch)/.test(t)){let u,c,f;if(t.startsWith("lab")?[s,u,c,f,l]=vr(t,e):[s,u,c,f,l]=es(t,e),J.test(r))return[s,u,c,f,l];[a,o,i,l]=ei([u,c,f,l])}else if(/^okl(?:ab|ch)/.test(t)){let u,c,f;if(t.startsWith("oklab")?[s,u,c,f,l]=ns(t,e):[s,u,c,f,l]=rs(t,e),J.test(r))return[s,u,c,f,l];[a,o,i,l]=In([u,c,f,l])}return r===U&&n==="srgb"?["srgb",a/W,o/W,i/W,l]:["rgb",Math.round(a),Math.round(o),Math.round(i),l]},We=(t,e={})=>{if(V(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n,format:r}=e;if(!Tn.test(t))switch(r){case U:return null;case X:return"";default:return["rgb",0,0,0,0]}const[s,a,o,i,l]=ht(t,e);if(J.test(r)||r===U&&s===n)return[s,a,o,i,l];const[u,c,f]=In([a,o,i],!0);return["rgb",u,c,f,l]},ua=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n,format:r}=e;let s,a,o,i,l,u,c,f;if(r===U){let m;if(t.startsWith(ot)?m=ht(t,e):m=mt(t,e),m===null)return m;if([s,u,c,f,l]=m,s===n)return[u,c,f,l];[a,o,i]=Z(Yr,[u,c,f],!0)}else if(t.startsWith(ot)){const[,m]=t.match(Tn),[d]=m.replace("/"," ").split(/\s+/);d==="srgb-linear"?[,a,o,i,l]=We(t,{format:at}):([,u,c,f,l]=ht(t),[a,o,i]=Z(Yr,[u,c,f],!0))}else[,u,c,f,l]=mt(t),[a,o,i]=Z(Yr,[u,c,f],!0);return[Math.min(Math.max(a,0),1),Math.min(Math.max(o,0),1),Math.min(Math.max(i,0),1),l]},ss=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;let r,s,a,o;if(n===U){let i;if(t.startsWith(ot)?i=We(t,e):i=cn(t,e),i===null)return i;[,r,s,a,o]=i}else if(t.startsWith(ot)){const[,i]=t.match(Tn),[l]=i.replace("/"," ").split(/\s+/);l==="srgb"?([,r,s,a,o]=We(t,{format:at}),r*=W,s*=W,a*=W):[,r,s,a,o]=We(t)}else/^(?:ok)?l(?:ab|ch)/.test(t)?([r,s,a,o]=ua(t),[r,s,a]=Jo([r,s,a])):[,r,s,a,o]=cn(t,{format:at});return[r,s,a,o]},si=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{d50:n,format:r}=e;let s,a,o,i;if(r===U){let l;if(t.startsWith(ot)?l=ht(t,e):l=mt(t,e),l===null)return l;[,s,a,o,i]=l}else if(t.startsWith(ot)){const[,l]=t.match(Tn),[u]=l.replace("/"," ").split(/\s+/);n?u==="xyz-d50"?[,s,a,o,i]=We(t,{format:at}):[,s,a,o,i]=ht(t,e):/^xyz(?:-d65)?$/.test(u)?[,s,a,o,i]=We(t,{format:at}):[,s,a,o,i]=ht(t)}else[,s,a,o,i]=mt(t,e);return[s,a,o,i]},ca=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;let r,s,a,o,i,l,u;if(Qs.test(t))return[,r,s,a,o]=ts(t,{format:"hsl"}),n==="hsl"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o];if(n===U){let c;if(t.startsWith(ot)?c=ht(t,e):c=mt(t,e),c===null)return c;[,i,l,u,o]=c}else t.startsWith(ot)?[,i,l,u,o]=ht(t):[,i,l,u,o]=mt(t);return[r,s,a]=Qo([i,l,u],!0),n==="hsl"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o]},ha=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;let r,s,a,o,i,l,u;if(ta.test(t))return[,r,s,a,o]=la(t,{format:"hwb"}),n==="hwb"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o];if(n===U){let c;if(t.startsWith(ot)?c=ht(t,e):c=mt(t,e),c===null)return c;[,i,l,u,o]=c}else t.startsWith(ot)?[,i,l,u,o]=ht(t):[,i,l,u,o]=mt(t);return[r,s,a]=R0([i,l,u],!0),n==="hwb"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o]},fa=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;let r,s,a,o,i,l,u;if(ea.test(t))return[,r,s,a,o]=vr(t,{format:at}),[r,s,a,o];if(n===U){let c;if(e.d50=!0,t.startsWith(ot)?c=ht(t,e):c=mt(t,e),c===null)return c;[,i,l,u,o]=c}else t.startsWith(ot)?[,i,l,u,o]=ht(t,{d50:!0}):[,i,l,u,o]=mt(t,{d50:!0});return[r,s,a]=ni([i,l,u],!0),[r,s,a,o]},pa=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;let r,s,a,o,i,l,u;if(na.test(t))return[,r,s,a,o]=es(t,{format:at}),[r,s,a,o];if(n===U){let c;if(e.d50=!0,t.startsWith(ot)?c=ht(t,e):c=mt(t,e),c===null)return c;[,i,l,u,o]=c}else t.startsWith(ot)?[,i,l,u,o]=ht(t,{d50:!0}):[,i,l,u,o]=mt(t,{d50:!0});return[r,s,a]=T0([i,l,u],!0),[r,s,a,o]},da=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;let r,s,a,o,i,l,u;if(ra.test(t))return[,r,s,a,o]=ns(t,{format:at}),[r,s,a,o];if(n===U){let c;if(t.startsWith(ot)?c=ht(t,e):c=mt(t,e),c===null)return c;[,i,l,u,o]=c}else t.startsWith(ot)?[,i,l,u,o]=ht(t):[,i,l,u,o]=mt(t);return[r,s,a]=ti([i,l,u],!0),[r,s,a,o]},ga=(t,e={})=>{if(V(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e;let r,s,a,o,i,l,u;if(sa.test(t))return[,r,s,a,o]=rs(t,{format:at}),[r,s,a,o];if(n===U){let c;if(t.startsWith(ot)?c=ht(t,e):c=mt(t,e),c===null)return c;[,i,l,u,o]=c}else t.startsWith(ot)?[,i,l,u,o]=ht(t):[,i,l,u,o]=mt(t);return[r,s,a]=W0([i,l,u],!0),[r,s,a,o]},_n=(t,e={})=>{if(V(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{format:n}=e,r=[];if(!Xo.test(t))if(t.startsWith(Ge)&&Ko.test(t)){const p=new RegExp(`^(?:${Ja}|${Es})$`),y=t.match(Ko);for(const E of y){let C=_n(E,{format:n===X?n:at});if(Array.isArray(C)){const[S,O,D,I,G]=C;if(O===0&&D===0&&I===0&&G===0){t="";break}p.test(S)?G===1?C=`color(${S} ${O} ${D} ${I})`:C=`color(${S} ${O} ${D} ${I} / ${G})`:G===1?C=`${S}(${O} ${D} ${I})`:C=`${S}(${O} ${D} ${I} / ${G})`}else if(!Xo.test(C)){t="";break}r.push(C),t=t.replace(E,C)}if(!t)return n===X?"":["rgb",0,0,0,0]}else return n===X?"":["rgb",0,0,0,0];let s,a,o,i,l,u;if(r.length&&n===X){const p=new RegExp(`^color-mix\\(\\s*in\\s+(${Cs})\\s*,`),[,y]=t.match(p);if(Jr.test(y)?[,s,a]=y.match(Jr):s=y,r.length===2){const E=r[0].replace(/(?=[()])/g,"\\"),C=new RegExp(`(${E})(?:\\s+(${Rt}))?`);[,o,i]=t.match(C);const S=r[1].replace(/(?=[()])/g,"\\"),O=new RegExp(`(${S})(?:\\s+(${Rt}))?`);[,l,u]=t.match(O)}else{const E=`(?:${en})(?:\\s+${Rt})?`,C=r[0].replace(/(?=[()])/g,"\\"),S=`${C}(?:\\s+${Rt})?`,O=`(${C})(?:\\s+(${Rt}))?`,D=new RegExp(`^(${en})(?:\\s+(${Rt}))?$`),I=new RegExp(`^${O}$`);if(new RegExp(`${O}\\s*\\)$`).test(t)){const G=new RegExp(`(${E})\\s*,\\s*(${S})\\s*\\)$`),[,Y,kt]=t.match(G);[,o,i]=Y.match(D),[,l,u]=kt.match(I)}else{const G=new RegExp(`(${S})\\s*,\\s*(${E})\\s*\\)$`),[,Y,kt]=t.match(G);[,o,i]=Y.match(I),[,l,u]=kt.match(D)}}}else{const[,p,y,E]=t.match(O0),C=new RegExp(`^(${en})(?:\\s+(${Rt}))?$`);[,o,i]=y.match(C),[,l,u]=E.match(C),Jr.test(p)?[,s,a]=Jr.exec(p):s=p}let c,f,m;if(i&&u){const p=parseFloat(i)/H,y=parseFloat(u)/H;if(p<0||p>1||y<0||y>1)return n===X?"":["rgb",0,0,0,0];const E=p+y;if(E===0)return n===X?"":["rgb",0,0,0,0];c=p/E,f=y/E,m=E<1?E:1}else{if(i){if(c=parseFloat(i)/H,c<0||c>1)return n===X?"":["rgb",0,0,0,0];f=1-c}else if(u){if(f=parseFloat(u)/H,f<0||f>1)return n===X?"":["rgb",0,0,0,0];c=1-f}else c=ue,f=ue;m=1}if(s==="xyz"&&(s="xyz-d65"),n===X){let p,y;if(o.startsWith(Ge))p=o;else if(o.startsWith(ot)){if(p=ht(o,e),Array.isArray(p)){const[E,C,S,O,D]=[...p];D===1?p=`color(${E} ${C} ${S} ${O})`:p=`color(${E} ${C} ${S} ${O} / ${D})`}}else{if(p=mt(o,e),p==="")return p;if(Array.isArray(p)){const[E,C,S,O,D]=[...p];D===1?E==="rgb"?p=`${E}(${C}, ${S}, ${O})`:p=`${E}(${C} ${S} ${O})`:E==="rgb"?p=`${E}a(${C}, ${S}, ${O}, ${D})`:p=`${E}(${C} ${S} ${O} / ${D})`}}if(l.startsWith(Ge))y=l;else if(l.startsWith(ot)){if(y=ht(l,e),Array.isArray(y)){const[E,C,S,O,D]=[...y];D===1?y=`color(${E} ${C} ${S} ${O})`:y=`color(${E} ${C} ${S} ${O} / ${D})`}}else{if(y=mt(l,e),y==="")return y;if(Array.isArray(y)){const[E,C,S,O,D]=[...y];D===1?E==="rgb"?y=`${E}(${C}, ${S}, ${O})`:y=`${E}(${C} ${S} ${O})`:E==="rgb"?y=`${E}a(${C}, ${S}, ${O}, ${D})`:y=`${E}(${C} ${S} ${O} / ${D})`}}if(i&&u)p+=` ${parseFloat(i)}%`,y+=` ${parseFloat(u)}%`;else if(i){const E=parseFloat(i);E!==H*ue&&(p+=` ${E}%`)}else if(u){const E=H-parseFloat(u);E!==H*ue&&(p+=` ${E}%`)}return a?`color-mix(in ${s} ${a} hue, ${p}, ${y})`:`color-mix(in ${s}, ${p}, ${y})`}let d,$,N,b;if(/^srgb(?:-linear)?$/.test(s)){let p,y;if(s==="srgb"?(pt.test(o)?p=[g,g,g,g]:p=ss(o,{colorSpace:s,format:U}),pt.test(l)?y=[g,g,g,g]:y=ss(l,{colorSpace:s,format:U})):(pt.test(o)?p=[g,g,g,g]:p=ua(o,{colorSpace:s,format:U}),pt.test(l)?y=[g,g,g,g]:y=ua(l,{colorSpace:s,format:U})),p===null||y===null)return["rgb",0,0,0,0];let[E,C,S,O]=p,[D,I,G,Y]=y;const kt=E===g&&D===g,se=C===g&&I===g,ae=S===g&&G===g,Se=O===g&&Y===g;[[E,C,S,O],[D,I,G,Y]]=gr([E,C,S,O],[D,I,G,Y],!0);const it=O*c,lt=Y*f;if(b=it+lt,b===0?(d=E*c+D*f,$=C*c+I*f,N=S*c+G*f):(d=(E*it+D*lt)/b,$=(C*it+I*lt)/b,N=(S*it+G*lt)/b,b=parseFloat(b.toFixed(3))),n===at)return[s,kt?g:A(d,P),se?g:A($,P),ae?g:A(N,P),Se?g:b*m];d*=W,$*=W,N*=W}else if(B0.test(s)){let p,y;if(pt.test(o)?p=[g,g,g,g]:p=si(o,{colorSpace:s,d50:s==="xyz-d50",format:U}),pt.test(l)?y=[g,g,g,g]:y=si(l,{colorSpace:s,d50:s==="xyz-d50",format:U}),p===null||y===null)return["rgb",0,0,0,0];let[E,C,S,O]=p,[D,I,G,Y]=y;const kt=E===g&&D===g,se=C===g&&I===g,ae=S===g&&G===g,Se=O===g&&Y===g;[[E,C,S,O],[D,I,G,Y]]=gr([E,C,S,O],[D,I,G,Y],!0);const it=O*c,lt=Y*f;b=it+lt;let Tt,Ft,St;if(b===0?(Tt=E*c+D*f,Ft=C*c+I*f,St=S*c+G*f):(Tt=(E*it+D*lt)/b,Ft=(C*it+I*lt)/b,St=(S*it+G*lt)/b,b=parseFloat(b.toFixed(3))),n===at)return[s,kt?g:A(Tt,P),se?g:A(Ft,P),ae?g:A(St,P),Se?g:b*m];s==="xyz-d50"?[d,$,N]=ei([Tt,Ft,St],!0):[d,$,N]=In([Tt,Ft,St],!0)}else if(/^h(?:sl|wb)$/.test(s)){let p,y;if(s==="hsl"?(pt.test(o)?p=[g,g,g,g]:p=ca(o,{colorSpace:s,format:U}),pt.test(l)?y=[g,g,g,g]:y=ca(l,{colorSpace:s,format:U})):(pt.test(o)?p=[g,g,g,g]:p=ha(o,{colorSpace:s,format:U}),pt.test(l)?y=[g,g,g,g]:y=ha(l,{colorSpace:s,format:U})),p===null||y===null)return["rgb",0,0,0,0];let[E,C,S,O]=p,[D,I,G,Y]=y;const kt=O===g&&Y===g;[[E,C,S,O],[D,I,G,Y]]=gr([E,C,S,O],[D,I,G,Y],!0),a&&([E,D]=Ho(E,D,a));const se=O*c,ae=Y*f;b=se+ae;const Se=(E*c+D*f)%ft;let it,lt;if(b===0?(it=C*c+I*f,lt=S*c+G*f):(it=(C*se+I*ae)/b,lt=(S*se+G*ae)/b,b=parseFloat(b.toFixed(3))),[d,$,N]=ss(`${s}(${Se} ${it} ${lt})`),n===at)return["srgb",A(d/W,P),A($/W,P),A(N/W,P),kt?g:b*m]}else if(/^(?:ok)?lab$/.test(s)){let p,y;if(s==="lab"?(pt.test(o)?p=[g,g,g,g]:p=fa(o,{colorSpace:s,format:U}),pt.test(l)?y=[g,g,g,g]:y=fa(l,{colorSpace:s,format:U})):(pt.test(o)?p=[g,g,g,g]:p=da(o,{colorSpace:s,format:U}),pt.test(l)?y=[g,g,g,g]:y=da(l,{colorSpace:s,format:U})),p===null||y===null)return["rgb",0,0,0,0];let[E,C,S,O]=p,[D,I,G,Y]=y;const kt=E===g&&D===g,se=C===g&&I===g,ae=S===g&&G===g,Se=O===g&&Y===g;[[E,C,S,O],[D,I,G,Y]]=gr([E,C,S,O],[D,I,G,Y],!0);const it=O*c,lt=Y*f;b=it+lt;let Tt,Ft,St;if(b===0?(Tt=E*c+D*f,Ft=C*c+I*f,St=S*c+G*f):(Tt=(E*it+D*lt)/b,Ft=(C*it+I*lt)/b,St=(S*it+G*lt)/b,b=parseFloat(b.toFixed(3))),n===at)return[s,kt?g:A(Tt,P),se?g:A(Ft,P),ae?g:A(St,P),Se?g:b*m];[,d,$,N]=cn(`${s}(${Tt} ${Ft} ${St})`)}else if(/^(?:ok)?lch$/.test(s)){let p,y;if(s==="lch"?(pt.test(o)?p=[g,g,g,g]:p=pa(o,{colorSpace:s,format:U}),pt.test(l)?y=[g,g,g,g]:y=pa(l,{colorSpace:s,format:U})):(pt.test(o)?p=[g,g,g,g]:p=ga(o,{colorSpace:s,format:U}),pt.test(l)?y=[g,g,g,g]:y=ga(l,{colorSpace:s,format:U})),p===null||y===null)return["rgb",0,0,0,0];let[E,C,S,O]=p,[D,I,G,Y]=y;const kt=E===g&&D===g,se=C===g&&I===g,ae=S===g&&G===g,Se=O===g&&Y===g;[[E,C,S,O],[D,I,G,Y]]=gr([E,C,S,O],[D,I,G,Y],!0),a&&([S,G]=Ho(S,G,a));const it=O*c,lt=Y*f;b=it+lt;const Tt=(S*c+G*f)%ft;let Ft,St;if(b===0?(Ft=E*c+D*f,St=C*c+I*f):(Ft=(E*it+D*lt)/b,St=(C*it+I*lt)/b,b=parseFloat(b.toFixed(3))),n===at)return[s,kt?g:A(Ft,P),se?g:A(St,P),ae?g:A(Tt,P),Se?g:b*m];[,d,$,N]=cn(`${s}(${Ft} ${St} ${Tt})`)}return["rgb",Math.round(d),Math.round($),Math.round(N),parseFloat((b*m).toFixed(3))]},_0=new RegExp(Ir),H0=new RegExp(to),z0=new RegExp(tr),K=new Xn({max:4096}),Te=(t,e={})=>{if(V(t)){if(t=t.trim(),!t)return null}else return null;const{customProperty:n}=e;let r;if(typeof n?.callback!="function"&&(r=`{preProcess:${t},opt:${Ct(e)}}`,K.has(r)))return K.get(r);if(z0.test(t)){const s=Ss(t,e);if(s)t=s;else return r&&K.set(r,s),null}if(H0.test(t))t=Vs(t,e);else if(_0.test(t)){const s=rr(t,e);if(s)t=s;else return r&&K.set(r,s),null}return t.startsWith("color-mix")&&(t=Dn(t,{format:at})),r&&K.set(r,t),t},U0=t=>{const e=typeof t=="number"&&`{numberToHex:${t}}`;if(e&&K.has(e))return K.get(e);const n=mr(t);return e&&K.set(e,n),n},G0=(t,e={})=>{if(V(t)){const o=Te(t,e);if(o)t=o.toLowerCase();else return null}else throw new TypeError(`${t} is not a string.`);const{alpha:n,customProperty:r}=e;let s;if(typeof r?.callback!="function"&&(s=`{colorToHex:${t},opt:${Ct(e)}}`,K.has(s)))return K.get(s);let a;return n?(e.format="hexAlpha",a=Dn(t,e)):(e.format="hex",a=Dn(t,e)),s&&K.set(s,a),a},j0=(t,e={})=>{if(V(t)){const a=Te(t,e);if(a)t=a.toLowerCase();else return[0,0,0,0]}else throw new TypeError(`${t} is not a string.`);const{customProperty:n}=e;let r;if(typeof n?.callback!="function"&&(r=`{colorToHsl:${t},opt:${Ct(e)}}`,K.has(r)))return K.get(r);e.format="hsl";const s=ca(t,e);return r&&K.set(r,s),s},q0=(t,e={})=>{if(V(t)){const a=Te(t,e);if(a)t=a.toLowerCase();else return[0,0,0,0]}else throw new TypeError(`${t} is not a string.`);const{customProperty:n}=e;let r;if(typeof n?.callback!="function"&&(r=`{colorToHwb:${t},opt:${Ct(e)}}`,K.has(r)))return K.get(r);e.format="hwb";const s=ha(t,e);return r&&K.set(r,s),s},V0=(t,e={})=>{if(V(t)){const a=Te(t,e);if(a)t=a.toLowerCase();else return[0,0,0,0]}else throw new TypeError(`${t} is not a string.`);const{customProperty:n}=e;let r;if(typeof n?.callback!="function"&&(r=`{colorToLab:${t},opt:${Ct(e)}}`,K.has(r)))return K.get(r);const s=fa(t,e);return r&&K.set(r,s),s},X0=(t,e={})=>{if(V(t)){const a=Te(t,e);if(a)t=a.toLowerCase();else return[0,0,0,0]}else throw new TypeError(`${t} is not a string.`);const{customProperty:n}=e;let r;if(typeof n?.callback!="function"&&(r=`{colorToLch:${t},opt:${Ct(e)}}`,K.has(r)))return K.get(r);const s=pa(t,e);return r&&K.set(r,s),s},K0=(t,e={})=>{if(V(t)){const a=Te(t,e);if(a)t=a.toLowerCase();else return[0,0,0,0]}else throw new TypeError(`${t} is not a string.`);const{customProperty:n}=e;let r;if(typeof n?.callback!="function"&&(r=`{colorToOklab:${t},opt:${Ct(e)}}`,K.has(r)))return K.get(r);const s=da(t,e);return r&&K.set(r,s),s},Z0=(t,e={})=>{if(V(t)){const a=Te(t,e);if(a)t=a.toLowerCase();else return[0,0,0,0]}else throw new TypeError(`${t} is not a string.`);const{customProperty:n}=e;let r;if(typeof n?.callback!="function"&&(r=`{colorToOklch:${t},opt:${Ct(e)}}`,K.has(r)))return K.get(r);const s=ga(t,e);return r&&K.set(r,s),s},ai=(t,e={})=>{if(V(t)){const a=Te(t,e);if(a)t=a.toLowerCase();else return[0,0,0,0]}else throw new TypeError(`${t} is not a string.`);const{customProperty:n}=e;let r;if(typeof n?.callback!="function"&&(r=`{colorToRgb:${t},opt:${Ct(e)}}`,K.has(r)))return K.get(r);const s=ss(t,e);return r&&K.set(r,s),s},oi=(t,e={})=>{if(V(t)){const a=Te(t,e);if(a)t=a.toLowerCase();else return[0,0,0,0]}else throw new TypeError(`${t} is not a string.`);const{customProperty:n}=e;let r;if(typeof n?.callback!="function"&&(r=`{colorToXyz:${t},opt:${Ct(e)}}`,K.has(r)))return K.get(r);let s;return t.startsWith("color(")?[,...s]=ht(t,e):[,...s]=mt(t,e),r&&K.set(r,s),s},Y0=(t,e={})=>(e.d50=!0,oi(t,e)),J0={colorToHex:G0,colorToHsl:j0,colorToHwb:q0,colorToLab:V0,colorToLch:X0,colorToOklab:K0,colorToOklch:Z0,colorToRgb:ai,colorToXyz:oi,colorToXyzD50:Y0,numberToHex:U0};export{J0 as convert,rr as cssCalc,cr as isColor,Dn as resolve};
/**
 * Bradford chromatic adaptation from D50 to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Bradford chromatic adaptation from D65 to D50
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_ChromAdapt.html
 */
/**
 * @param {number} hue - Hue as degrees 0..360
 * @param {number} sat - Saturation as percentage 0..100
 * @param {number} light - Lightness as percentage 0..100
 * @return {number[]} Array of sRGB components; in-gamut colors in range [0..1]
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js
 */
/**
 * @param {number} hue -  Hue as degrees 0..360
 * @param {number} white -  Whiteness as percentage 0..100
 * @param {number} black -  Blackness as percentage 0..100
 * @return {number[]} Array of RGB components 0..1
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js
 */
/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert Lab to D50-adapted XYZ
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */
/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */
/**
 * Given OKLab, convert to XYZ relative to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */
/**
 * Assuming XYZ is relative to D50, convert to CIE Lab
 * from CIE standard, which now defines these as a rational fraction
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * XYZ <-> LMS matrices recalculated for consistent reference white
 * @see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484
 */
/**
 * Convert XYZ to linear-light rec2020
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert XYZ to linear-light P3
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert D50 XYZ to linear-light prophoto-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */
/**
 * Convert XYZ to linear-light a98-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert an array of linear-light rec2020 RGB  in the range 0.0-1.0
 * to gamma corrected form ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert an array of linear-light sRGB values in the range 0.0-1.0 to gamma corrected form
 * Extended transfer function:
 *  For negative values, linear portion extends on reflection
 *  of axis, then uses reflected pow below that
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */
/**
 * Convert an array of linear-light display-p3 RGB in the range 0.0-1.0
 * to gamma corrected form
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert an array of linear-light prophoto-rgb in the range 0.0-1.0
 * to gamma corrected form.
 * Transfer curve is gamma 1.8 with a small linear portion.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert an array of linear-light a98-rgb in the range 0.0-1.0
 * to gamma corrected form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert an array of rec2020 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 * ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert an array of linear-light rec2020 values to CIE XYZ
 * using  D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */
/**
 * Convert an array of of sRGB values where in-gamut values are in the range
 * [0 - 1] to linear light (un-companded) form.
 * Extended transfer function:
 *  For negative values, linear portion is extended on reflection of axis,
 *  then reflected power function is used.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */
/**
 * Convert an array of display-p3 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert an array of linear-light display-p3 values to CIE XYZ
 * using D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */
/**
 * Convert an array of prophoto-rgb values where in-gamut Colors are in the
 * range [0.0 - 1.0] to linear light (un-companded) form. Transfer curve is
 * gamma 1.8 with a small linear portion. Extended transfer function
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert an array of linear-light prophoto-rgb values to CIE D50 XYZ.
 * Matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see see https://github.com/w3c/csswg-drafts/issues/7675
 */
/**
 * Convert an array of linear-light a98-rgb values to CIE XYZ
 * http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * has greater numerical precision than section ******* of
 * https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * but the values below were calculated from first principles
 * from the chromaticity coordinates of R G B W
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * @see https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/matrixmaker.html
 */
/**
 * Convert an array of linear-light sRGB values to CIE XYZ
 * using sRGB's own white, D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * Convert an array of gamma-corrected sRGB values in the 0.0 to 1.0 range to HSL.
 *
 * @param {Color} RGB [r, g, b]
 * - Red component 0..1
 * - Green component 0..1
 * - Blue component 0..1
 * @return {number[]} Array of HSL values: Hue as degrees 0..360, Saturation and Lightness as percentages 0..100
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/utilities.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/better-rgbToHsl.js
 */
/**
 * Convert an array of a98-rgb values in the range 0.0 - 1.0
 * to linear light (un-companded) form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */
/**
 * @license MIT https://github.com/facelessuser/coloraide/blob/main/LICENSE.md
 */
