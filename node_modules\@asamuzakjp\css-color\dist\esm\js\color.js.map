{"version": 3, "file": "color.js", "sources": ["../../../src/js/color.ts"], "sourcesContent": ["/**\n * color.js\n *\n * Ref: CSS Color Module Level 4\n *      Sample code for Color Conversions\n *      https://w3c.github.io/csswg-drafts/css-color-4/#color-conversion-code\n */\n\nimport { isString } from './common';\nimport { interpolateHue, roundToPrecision } from './util';\n\n/* constants */\nimport {\n  ANGLE,\n  CS_HUE_CAPT,\n  CS_MIX,\n  CS_RGB,\n  CS_XYZ,\n  FN_COLOR,\n  FN_MIX,\n  NONE,\n  NUM,\n  PCT,\n  SYN_COLOR_TYPE,\n  SYN_FN_COLOR,\n  SYN_HSL,\n  SYN_HSL_LV3,\n  SYN_LCH,\n  SYN_MIX,\n  SYN_MIX_CAPT,\n  SYN_MOD,\n  SYN_RGB_LV3,\n  VAL_COMP,\n  VAL_SPEC\n} from './constant.js';\n\nconst VAL_MIX = 'mixValue';\n\n/* numeric constants */\nconst PPTH = 0.001;\nconst HALF = 0.5;\nconst DUO = 2;\nconst TRIA = 3;\nconst QUAT = 4;\nconst OCT = 8;\nconst DEC = 10;\nconst DOZ = 12;\nconst HEX = 16;\nconst SEXA = 60;\nconst DEG = 360;\nconst MAX_PCT = 100;\nconst MAX_RGB = 255;\nconst POW_SQR = 2;\nconst POW_CUBE = 3;\nconst POW_LINEAR = 2.4;\nconst LINEAR_COEF = 12.92;\nconst LINEAR_OFFSET = 0.055;\nconst LAB_L = 116;\nconst LAB_A = 500;\nconst LAB_B = 200;\nconst LAB_EPSILON = 216 / 24389;\nconst LAB_KAPPA = 24389 / 27;\n\n/* white point */\nconst D50 = [0.3457 / 0.3585, 1.0, (1.0 - 0.3457 - 0.3585) / 0.3585];\nconst MATRIX_D50_TO_D65 = [\n  [0.955473421488075, -0.02309845494876471, 0.06325924320057072],\n  [-0.0283697093338637, 1.0099953980813041, 0.021041441191917323],\n  [0.012314014864481998, -0.020507649298898964, 1.330365926242124]\n];\nconst MATRIX_D65_TO_D50 = [\n  [1.0479297925449969, 0.022946870601609652, -0.05019226628920524],\n  [0.02962780877005599, 0.9904344267538799, -0.017073799063418826],\n  [-0.009243040646204504, 0.015055191490298152, 0.7518742814281371]\n];\n\n/* color space */\nconst MATRIX_L_RGB_TO_XYZ = [\n  [506752 / 1228815, 87881 / 245763, 12673 / 70218],\n  [87098 / 409605, 175762 / 245763, 12673 / 175545],\n  [7918 / 409605, 87881 / 737289, 1001167 / 1053270]\n];\nconst MATRIX_XYZ_TO_L_RGB = [\n  [12831 / 3959, -329 / 214, -1974 / 3959],\n  [-851781 / 878810, 1648619 / 878810, 36519 / 878810],\n  [705 / 12673, -2585 / 12673, 705 / 667]\n];\nconst MATRIX_XYZ_TO_LMS = [\n  [0.819022437996703, 0.3619062600528904, -0.1288737815209879],\n  [0.0329836539323885, 0.9292868615863434, 0.0361446663506424],\n  [0.0481771893596242, 0.2642395317527308, 0.6335478284694309]\n];\nconst MATRIX_LMS_TO_XYZ = [\n  [1.2268798758459243, -0.5578149944602171, 0.2813910456659647],\n  [-0.0405757452148008, 1.112286803280317, -0.0717110580655164],\n  [-0.0763729366746601, -0.4214933324022432, 1.5869240198367816]\n];\nconst MATRIX_OKLAB_TO_LMS = [\n  [1.0, 0.3963377773761749, 0.2158037573099136],\n  [1.0, -0.1055613458156586, -0.0638541728258133],\n  [1.0, -0.0894841775298119, -1.2914855480194092]\n];\nconst MATRIX_LMS_TO_OKLAB = [\n  [0.210454268309314, 0.7936177747023054, -0.0040720430116193],\n  [1.9779985324311684, -2.4285922420485799, 0.450593709617411],\n  [0.0259040424655478, 0.7827717124575296, -0.8086757549230774]\n];\nconst MATRIX_P3_TO_XYZ = [\n  [608311 / 1250200, 189793 / 714400, 198249 / 1000160],\n  [35783 / 156275, 247089 / 357200, 198249 / 2500400],\n  [0 / 1, 32229 / 714400, 5220557 / 5000800]\n];\nconst MATRIX_REC2020_TO_XYZ = [\n  [63426534 / 99577255, 20160776 / 139408157, 47086771 / 278816314],\n  [26158966 / 99577255, 472592308 / 697040785, 8267143 / 139408157],\n  [0 / 1, 19567812 / 697040785, 295819943 / 278816314]\n];\nconst MATRIX_A98_TO_XYZ = [\n  [573536 / 994567, 263643 / 1420810, 187206 / 994567],\n  [591459 / 1989134, 6239551 / 9945670, 374412 / 4972835],\n  [53769 / 1989134, 351524 / 4972835, 4929758 / 4972835]\n];\nconst MATRIX_PROPHOTO_TO_XYZ_D50 = [\n  [0.7977666449006423, 0.13518129740053308, 0.0313477341283922],\n\n  [0.2880748288194013, 0.711835234241873, 0.00008993693872564],\n\n  [0.0, 0.0, 0.8251046025104602]\n];\n\n/* regexp */\nconst REG_COLOR = new RegExp(`^(?:${SYN_COLOR_TYPE})$`);\nconst REG_CS_HUE = new RegExp(`^${CS_HUE_CAPT}$`);\nconst REG_CS_XYZ = /^xyz(?:-d(?:50|65))?$/;\nconst REG_CURRENT = /^currentColor$/i;\nconst REG_FN_COLOR = new RegExp(`^color\\\\(\\\\s*(${SYN_FN_COLOR})\\\\s*\\\\)$`);\nconst REG_HSL = new RegExp(`^hsla?\\\\(\\\\s*(${SYN_HSL}|${SYN_HSL_LV3})\\\\s*\\\\)$`);\nconst REG_HWB = new RegExp(`^hwb\\\\(\\\\s*(${SYN_HSL})\\\\s*\\\\)$`);\nconst REG_LAB = new RegExp(`^lab\\\\(\\\\s*(${SYN_MOD})\\\\s*\\\\)$`);\nconst REG_LCH = new RegExp(`^lch\\\\(\\\\s*(${SYN_LCH})\\\\s*\\\\)$`);\nconst REG_MIX = new RegExp(`^${SYN_MIX}$`);\nconst REG_MIX_CAPT = new RegExp(`^${SYN_MIX_CAPT}$`);\nconst REG_MIX_NEST = new RegExp(`${SYN_MIX}`, 'g');\nconst REG_OKLAB = new RegExp(`^oklab\\\\(\\\\s*(${SYN_MOD})\\\\s*\\\\)$`);\nconst REG_OKLCH = new RegExp(`^oklch\\\\(\\\\s*(${SYN_LCH})\\\\s*\\\\)$`);\nconst REG_SPEC = /^(?:specifi|comput)edValue$/;\n\n/* named colors */\nexport const NAMED_COLORS = {\n  aliceblue: [0xf0, 0xf8, 0xff],\n  antiquewhite: [0xfa, 0xeb, 0xd7],\n  aqua: [0x00, 0xff, 0xff],\n  aquamarine: [0x7f, 0xff, 0xd4],\n  azure: [0xf0, 0xff, 0xff],\n  beige: [0xf5, 0xf5, 0xdc],\n  bisque: [0xff, 0xe4, 0xc4],\n  black: [0x00, 0x00, 0x00],\n  blanchedalmond: [0xff, 0xeb, 0xcd],\n  blue: [0x00, 0x00, 0xff],\n  blueviolet: [0x8a, 0x2b, 0xe2],\n  brown: [0xa5, 0x2a, 0x2a],\n  burlywood: [0xde, 0xb8, 0x87],\n  cadetblue: [0x5f, 0x9e, 0xa0],\n  chartreuse: [0x7f, 0xff, 0x00],\n  chocolate: [0xd2, 0x69, 0x1e],\n  coral: [0xff, 0x7f, 0x50],\n  cornflowerblue: [0x64, 0x95, 0xed],\n  cornsilk: [0xff, 0xf8, 0xdc],\n  crimson: [0xdc, 0x14, 0x3c],\n  cyan: [0x00, 0xff, 0xff],\n  darkblue: [0x00, 0x00, 0x8b],\n  darkcyan: [0x00, 0x8b, 0x8b],\n  darkgoldenrod: [0xb8, 0x86, 0x0b],\n  darkgray: [0xa9, 0xa9, 0xa9],\n  darkgreen: [0x00, 0x64, 0x00],\n  darkgrey: [0xa9, 0xa9, 0xa9],\n  darkkhaki: [0xbd, 0xb7, 0x6b],\n  darkmagenta: [0x8b, 0x00, 0x8b],\n  darkolivegreen: [0x55, 0x6b, 0x2f],\n  darkorange: [0xff, 0x8c, 0x00],\n  darkorchid: [0x99, 0x32, 0xcc],\n  darkred: [0x8b, 0x00, 0x00],\n  darksalmon: [0xe9, 0x96, 0x7a],\n  darkseagreen: [0x8f, 0xbc, 0x8f],\n  darkslateblue: [0x48, 0x3d, 0x8b],\n  darkslategray: [0x2f, 0x4f, 0x4f],\n  darkslategrey: [0x2f, 0x4f, 0x4f],\n  darkturquoise: [0x00, 0xce, 0xd1],\n  darkviolet: [0x94, 0x00, 0xd3],\n  deeppink: [0xff, 0x14, 0x93],\n  deepskyblue: [0x00, 0xbf, 0xff],\n  dimgray: [0x69, 0x69, 0x69],\n  dimgrey: [0x69, 0x69, 0x69],\n  dodgerblue: [0x1e, 0x90, 0xff],\n  firebrick: [0xb2, 0x22, 0x22],\n  floralwhite: [0xff, 0xfa, 0xf0],\n  forestgreen: [0x22, 0x8b, 0x22],\n  fuchsia: [0xff, 0x00, 0xff],\n  gainsboro: [0xdc, 0xdc, 0xdc],\n  ghostwhite: [0xf8, 0xf8, 0xff],\n  gold: [0xff, 0xd7, 0x00],\n  goldenrod: [0xda, 0xa5, 0x20],\n  gray: [0x80, 0x80, 0x80],\n  green: [0x00, 0x80, 0x00],\n  greenyellow: [0xad, 0xff, 0x2f],\n  grey: [0x80, 0x80, 0x80],\n  honeydew: [0xf0, 0xff, 0xf0],\n  hotpink: [0xff, 0x69, 0xb4],\n  indianred: [0xcd, 0x5c, 0x5c],\n  indigo: [0x4b, 0x00, 0x82],\n  ivory: [0xff, 0xff, 0xf0],\n  khaki: [0xf0, 0xe6, 0x8c],\n  lavender: [0xe6, 0xe6, 0xfa],\n  lavenderblush: [0xff, 0xf0, 0xf5],\n  lawngreen: [0x7c, 0xfc, 0x00],\n  lemonchiffon: [0xff, 0xfa, 0xcd],\n  lightblue: [0xad, 0xd8, 0xe6],\n  lightcoral: [0xf0, 0x80, 0x80],\n  lightcyan: [0xe0, 0xff, 0xff],\n  lightgoldenrodyellow: [0xfa, 0xfa, 0xd2],\n  lightgray: [0xd3, 0xd3, 0xd3],\n  lightgreen: [0x90, 0xee, 0x90],\n  lightgrey: [0xd3, 0xd3, 0xd3],\n  lightpink: [0xff, 0xb6, 0xc1],\n  lightsalmon: [0xff, 0xa0, 0x7a],\n  lightseagreen: [0x20, 0xb2, 0xaa],\n  lightskyblue: [0x87, 0xce, 0xfa],\n  lightslategray: [0x77, 0x88, 0x99],\n  lightslategrey: [0x77, 0x88, 0x99],\n  lightsteelblue: [0xb0, 0xc4, 0xde],\n  lightyellow: [0xff, 0xff, 0xe0],\n  lime: [0x00, 0xff, 0x00],\n  limegreen: [0x32, 0xcd, 0x32],\n  linen: [0xfa, 0xf0, 0xe6],\n  magenta: [0xff, 0x00, 0xff],\n  maroon: [0x80, 0x00, 0x00],\n  mediumaquamarine: [0x66, 0xcd, 0xaa],\n  mediumblue: [0x00, 0x00, 0xcd],\n  mediumorchid: [0xba, 0x55, 0xd3],\n  mediumpurple: [0x93, 0x70, 0xdb],\n  mediumseagreen: [0x3c, 0xb3, 0x71],\n  mediumslateblue: [0x7b, 0x68, 0xee],\n  mediumspringgreen: [0x00, 0xfa, 0x9a],\n  mediumturquoise: [0x48, 0xd1, 0xcc],\n  mediumvioletred: [0xc7, 0x15, 0x85],\n  midnightblue: [0x19, 0x19, 0x70],\n  mintcream: [0xf5, 0xff, 0xfa],\n  mistyrose: [0xff, 0xe4, 0xe1],\n  moccasin: [0xff, 0xe4, 0xb5],\n  navajowhite: [0xff, 0xde, 0xad],\n  navy: [0x00, 0x00, 0x80],\n  oldlace: [0xfd, 0xf5, 0xe6],\n  olive: [0x80, 0x80, 0x00],\n  olivedrab: [0x6b, 0x8e, 0x23],\n  orange: [0xff, 0xa5, 0x00],\n  orangered: [0xff, 0x45, 0x00],\n  orchid: [0xda, 0x70, 0xd6],\n  palegoldenrod: [0xee, 0xe8, 0xaa],\n  palegreen: [0x98, 0xfb, 0x98],\n  paleturquoise: [0xaf, 0xee, 0xee],\n  palevioletred: [0xdb, 0x70, 0x93],\n  papayawhip: [0xff, 0xef, 0xd5],\n  peachpuff: [0xff, 0xda, 0xb9],\n  peru: [0xcd, 0x85, 0x3f],\n  pink: [0xff, 0xc0, 0xcb],\n  plum: [0xdd, 0xa0, 0xdd],\n  powderblue: [0xb0, 0xe0, 0xe6],\n  purple: [0x80, 0x00, 0x80],\n  rebeccapurple: [0x66, 0x33, 0x99],\n  red: [0xff, 0x00, 0x00],\n  rosybrown: [0xbc, 0x8f, 0x8f],\n  royalblue: [0x41, 0x69, 0xe1],\n  saddlebrown: [0x8b, 0x45, 0x13],\n  salmon: [0xfa, 0x80, 0x72],\n  sandybrown: [0xf4, 0xa4, 0x60],\n  seagreen: [0x2e, 0x8b, 0x57],\n  seashell: [0xff, 0xf5, 0xee],\n  sienna: [0xa0, 0x52, 0x2d],\n  silver: [0xc0, 0xc0, 0xc0],\n  skyblue: [0x87, 0xce, 0xeb],\n  slateblue: [0x6a, 0x5a, 0xcd],\n  slategray: [0x70, 0x80, 0x90],\n  slategrey: [0x70, 0x80, 0x90],\n  snow: [0xff, 0xfa, 0xfa],\n  springgreen: [0x00, 0xff, 0x7f],\n  steelblue: [0x46, 0x82, 0xb4],\n  tan: [0xd2, 0xb4, 0x8c],\n  teal: [0x00, 0x80, 0x80],\n  thistle: [0xd8, 0xbf, 0xd8],\n  tomato: [0xff, 0x63, 0x47],\n  turquoise: [0x40, 0xe0, 0xd0],\n  violet: [0xee, 0x82, 0xee],\n  wheat: [0xf5, 0xde, 0xb3],\n  white: [0xff, 0xff, 0xff],\n  whitesmoke: [0xf5, 0xf5, 0xf5],\n  yellow: [0xff, 0xff, 0x00],\n  yellowgreen: [0x9a, 0xcd, 0x32]\n} as const;\n\n/**\n * validate color components\n * @param {Array} arr - array of color components\n * @param {object} [opt] - options\n * @param {boolean} [opt.alpha] - alpha channel\n * @param {number} [opt.minLength] - min length\n * @param {number} [opt.maxLength] - max length\n * @param {number} [opt.minRange] - min range\n * @param {number} [opt.maxRange] - max range\n * @param {boolean} [opt.validateRange] - validate range\n * @returns {Array} - validated color components\n */\nexport const validateColorComponents = (\n  arr: Array<any>,\n  opt: {\n    alpha?: boolean;\n    minLength?: number;\n    maxLength?: number;\n    minRange?: number;\n    maxRange?: number;\n    validateRange?: boolean;\n  } = {}\n): Array<any> => {\n  if (!Array.isArray(arr)) {\n    throw new TypeError(`${arr} is not an array.`);\n  }\n  const {\n    alpha = false,\n    minLength = TRIA,\n    maxLength = QUAT,\n    minRange = 0,\n    maxRange = 1,\n    validateRange = true\n  } = opt;\n  if (!Number.isFinite(minLength)) {\n    throw new TypeError(`${minLength} is not a number.`);\n  }\n  if (!Number.isFinite(maxLength)) {\n    throw new TypeError(`${maxLength} is not a number.`);\n  }\n  if (!Number.isFinite(minRange)) {\n    throw new TypeError(`${minRange} is not a number.`);\n  }\n  if (!Number.isFinite(maxRange)) {\n    throw new TypeError(`${maxRange} is not a number.`);\n  }\n  const l = arr.length;\n  if (l < minLength || l > maxLength) {\n    throw new Error(`Unexpected array length ${l}.`);\n  }\n  let i = 0;\n  while (i < l) {\n    const v = arr[i];\n    if (!Number.isFinite(v)) {\n      throw new TypeError(`${v} is not a number.`);\n    } else if (i < TRIA && validateRange && (v < minRange || v > maxRange)) {\n      throw new RangeError(`${v} is not between ${minRange} and ${maxRange}.`);\n    } else if (i === TRIA && (v < 0 || v > 1)) {\n      throw new RangeError(`${v} is not between 0 and 1.`);\n    }\n    i++;\n  }\n  if (alpha && l === TRIA) {\n    arr.push(1);\n  }\n  return arr;\n};\n\n/**\n * transform matrix\n * @param {Array.<Array.<number>>} mtx - 3 * 3 matrix\n * @param {Array.<number>} vct - vector\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [p1, p2, p3]\n */\nexport const transformMatrix = (\n  mtx: Array<Array<number>>,\n  vct: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  if (!Array.isArray(mtx)) {\n    throw new TypeError(`${mtx} is not an array.`);\n  } else if (mtx.length !== TRIA) {\n    throw new Error(`Unexpected array length ${mtx.length}.`);\n  } else if (!skip) {\n    for (let i of mtx) {\n      i = validateColorComponents(i, {\n        maxLength: TRIA,\n        validateRange: false\n      });\n    }\n  }\n  const [[r1c1, r1c2, r1c3], [r2c1, r2c2, r2c3], [r3c1, r3c2, r3c3]] = mtx as [\n    [number, number, number],\n    [number, number, number],\n    [number, number, number]\n  ];\n  let v1, v2, v3;\n  if (skip) {\n    [v1, v2, v3] = vct;\n  } else {\n    [v1, v2, v3] = validateColorComponents(vct, {\n      maxLength: TRIA,\n      validateRange: false\n    });\n  }\n  const p1 = r1c1 * v1 + r1c2 * v2 + r1c3 * v3;\n  const p2 = r2c1 * v1 + r2c2 * v2 + r2c3 * v3;\n  const p3 = r3c1 * v1 + r3c2 * v2 + r3c3 * v3;\n  return [p1, p2, p3];\n};\n\n/**\n * normalize color components\n * @param {Array} colorA - array of color components [v1, v2, v3, v4]\n * @param {Array} colorB - array of color components [v1, v2, v3, v4]\n * @param {boolean} skip - skip validate\n * @returns {Array.<Array.<number>>} - [colorA, colorB]\n */\nexport const normalizeColorComponents = (\n  colorA: Array<any>,\n  colorB: Array<any>,\n  skip: boolean = false\n): Array<Array<number>> => {\n  if (!Array.isArray(colorA)) {\n    throw new TypeError(`${colorA} is not an array.`);\n  } else if (colorA.length !== QUAT) {\n    throw new Error(`Unexpected array length ${colorA.length}.`);\n  }\n  if (!Array.isArray(colorB)) {\n    throw new TypeError(`${colorB} is not an array.`);\n  } else if (colorB.length !== QUAT) {\n    throw new Error(`Unexpected array length ${colorB.length}.`);\n  }\n  let i = 0;\n  while (i < QUAT) {\n    if (colorA[i] === NONE && colorB[i] === NONE) {\n      colorA[i] = 0;\n      colorB[i] = 0;\n    } else if (colorA[i] === NONE) {\n      colorA[i] = colorB[i];\n    } else if (colorB[i] === NONE) {\n      colorB[i] = colorA[i];\n    }\n    i++;\n  }\n  if (!skip) {\n    colorA = validateColorComponents(colorA, {\n      minLength: QUAT,\n      validateRange: false\n    });\n    colorB = validateColorComponents(colorB, {\n      minLength: QUAT,\n      validateRange: false\n    });\n  }\n  return [colorA, colorB];\n};\n\n/**\n * number to hex string\n * @param {number} value - color value\n * @returns {string} - hex string\n */\nexport const numberToHexString = (value: number): string => {\n  if (!Number.isFinite(value)) {\n    throw new TypeError(`${value} is not a number.`);\n  } else {\n    value = Math.round(value);\n    if (value < 0 || value > MAX_RGB) {\n      throw new RangeError(`${value} is not between 0 and ${MAX_RGB}.`);\n    }\n  }\n  let hex = value.toString(HEX);\n  if (hex.length === 1) {\n    hex = `0${hex}`;\n  }\n  return hex;\n};\n\n/**\n * angle to deg\n * @param {string} angle - angle\n * @returns {number} - deg: 0..360\n */\nexport const angleToDeg = (angle: string): number => {\n  if (isString(angle)) {\n    angle = angle.trim();\n  } else {\n    throw new TypeError(`${angle} is not a string.`);\n  }\n  const GRAD = DEG / 400;\n  const RAD = DEG / (Math.PI * DUO);\n  const reg = new RegExp(`^(${NUM})(${ANGLE})?$`);\n  if (!reg.test(angle)) {\n    throw new SyntaxError(`Invalid property value: ${angle}`);\n  }\n  const [, val, unit] = angle.match(reg) as [string, string, string];\n  const value = val[0] === '.' ? `0${val}` : val;\n  let deg;\n  switch (unit) {\n    case 'grad':\n      deg = parseFloat(value) * GRAD;\n      break;\n    case 'rad':\n      deg = parseFloat(value) * RAD;\n      break;\n    case 'turn':\n      deg = parseFloat(value) * DEG;\n      break;\n    default:\n      deg = parseFloat(value);\n  }\n  deg %= DEG;\n  if (deg < 0) {\n    deg += DEG;\n  } else if (Object.is(deg, -0)) {\n    deg = 0;\n  }\n  return deg;\n};\n\n/**\n * parse alpha\n * @param {?string} [_alpha] - alpha value\n * @returns {number} - alpha: 0..1\n */\nexport const parseAlpha = (_alpha?: string | null): number => {\n  let alpha: number | string | null | undefined = _alpha;\n  if (isString(alpha)) {\n    alpha = alpha.trim();\n    if (!alpha) {\n      alpha = 1;\n    } else if (alpha === NONE) {\n      alpha = 0;\n    } else {\n      if (alpha[0] === '.') {\n        alpha = `0${alpha}`;\n      }\n      if (alpha.endsWith('%')) {\n        alpha = parseFloat(alpha) / MAX_PCT;\n      } else {\n        alpha = parseFloat(alpha);\n      }\n      if (!Number.isFinite(alpha)) {\n        throw new TypeError(`${alpha} is not a number.`);\n      }\n      if (alpha < PPTH) {\n        alpha = 0;\n      } else if (alpha > 1) {\n        alpha = 1;\n      } else {\n        alpha = parseFloat(alpha.toFixed(3));\n      }\n    }\n  } else {\n    alpha = 1;\n  }\n  return alpha;\n};\n\n/**\n * parse hex alpha\n * @param {string} value - alpha value in hex string\n * @returns {number} - alpha: 0..1\n */\nexport const parseHexAlpha = (value: string): number => {\n  if (isString(value)) {\n    if (value === '') {\n      throw new SyntaxError('Invalid property value: (empty string)');\n    }\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  let alpha = parseInt(value, HEX);\n  if (alpha <= 0) {\n    return 0;\n  }\n  if (alpha >= MAX_RGB) {\n    return 1;\n  }\n  const alphaMap = new Map();\n  for (let i = 1; i < MAX_PCT; i++) {\n    alphaMap.set(Math.round((i * MAX_RGB) / MAX_PCT), i);\n  }\n  if (alphaMap.has(alpha)) {\n    alpha = alphaMap.get(alpha) / MAX_PCT;\n  } else {\n    alpha = Math.round(alpha / MAX_RGB / PPTH) * PPTH;\n  }\n  return parseFloat(alpha.toFixed(3));\n};\n\n/**\n * convert rgb to linear rgb\n * @param {Array.<number>} rgb - [r, g, b] r|g|b: 0..255\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [r, g, b] r|g|b: 0..1\n */\nexport const convertRgbToLinearRgb = (\n  rgb: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  let rr, gg, bb;\n  if (skip) {\n    [rr, gg, bb] = rgb;\n  } else {\n    [rr, gg, bb] = validateColorComponents(rgb, {\n      maxLength: TRIA,\n      maxRange: MAX_RGB\n    });\n  }\n  let r = rr / MAX_RGB;\n  let g = gg / MAX_RGB;\n  let b = bb / MAX_RGB;\n  const COND_POW = 0.04045;\n  if (r > COND_POW) {\n    r = Math.pow((r + LINEAR_OFFSET) / (1 + LINEAR_OFFSET), POW_LINEAR);\n  } else {\n    r /= LINEAR_COEF;\n  }\n  if (g > COND_POW) {\n    g = Math.pow((g + LINEAR_OFFSET) / (1 + LINEAR_OFFSET), POW_LINEAR);\n  } else {\n    g /= LINEAR_COEF;\n  }\n  if (b > COND_POW) {\n    b = Math.pow((b + LINEAR_OFFSET) / (1 + LINEAR_OFFSET), POW_LINEAR);\n  } else {\n    b /= LINEAR_COEF;\n  }\n  return [r, g, b];\n};\n\n/**\n * convert rgb to xyz\n * @param {Array.<number>} rgb - [r, g, b, ?alpha] r|g|b: 0..255 alpha: 0..1\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [x, y, z, alpha]\n */\nexport const convertRgbToXyz = (\n  rgb: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  let r, g, b, alpha;\n  if (skip) {\n    [r, g, b, alpha] = rgb;\n  } else {\n    [r, g, b, alpha] = validateColorComponents(rgb, {\n      alpha: true,\n      maxRange: MAX_RGB\n    });\n  }\n  const [rr, gg, bb] = convertRgbToLinearRgb([r, g, b], true);\n  const [x, y, z] = transformMatrix(MATRIX_L_RGB_TO_XYZ, [rr!, gg!, bb!], true);\n  return [x, y, z, alpha];\n};\n\n/**\n * convert rgb to xyz-d50\n * @param {Array.<number>} rgb - [r, g, b, ?alpha] r|g|b: 0..255 alpha: 0..1\n * @returns {Array.<number>} - [x, y, z, alpha]\n */\nexport const convertRgbToXyzD50 = (rgb: Array<number>): Array<number> => {\n  const [xx, yy, zz, alpha] = convertRgbToXyz(rgb);\n  const [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [xx!, yy!, zz!], true);\n  return [x!, y!, z!, alpha!];\n};\n\n/**\n * convert rgb to hex color\n * @param {Array.<number>} rgb - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n * @returns {string} - hex color\n */\nexport const convertRgbToHex = (rgb: Array<number>): string => {\n  const [r, g, b, alpha] = validateColorComponents(rgb, {\n    alpha: true,\n    maxRange: MAX_RGB\n  });\n  const rr = numberToHexString(r);\n  const gg = numberToHexString(g);\n  const bb = numberToHexString(b);\n  const aa = numberToHexString(alpha * MAX_RGB);\n  let hex;\n  if (aa === 'ff') {\n    hex = `#${rr}${gg}${bb}`;\n  } else {\n    hex = `#${rr}${gg}${bb}${aa}`;\n  }\n  return hex;\n};\n\n/**\n * convert linear rgb to rgb\n * @param {Array.<number>} rgb - [r, g, b] r|g|b: 0..1\n * @param {boolean} round - round result\n * @returns {Array.<number>} - [r, g, b] r|g|b: 0..255\n */\nexport const convertLinearRgbToRgb = (\n  rgb: Array<number>,\n  round: boolean = false\n): Array<number> => {\n  let [r, g, b] = validateColorComponents(rgb, {\n    maxLength: TRIA\n  });\n  const COND_POW = 809 / 258400;\n  if (r > COND_POW) {\n    r = Math.pow(r, 1 / POW_LINEAR) * (1 + LINEAR_OFFSET) - LINEAR_OFFSET;\n  } else {\n    r *= LINEAR_COEF;\n  }\n  r *= MAX_RGB;\n  if (g > COND_POW) {\n    g = Math.pow(g, 1 / POW_LINEAR) * (1 + LINEAR_OFFSET) - LINEAR_OFFSET;\n  } else {\n    g *= LINEAR_COEF;\n  }\n  g *= MAX_RGB;\n  if (b > COND_POW) {\n    b = Math.pow(b, 1 / POW_LINEAR) * (1 + LINEAR_OFFSET) - LINEAR_OFFSET;\n  } else {\n    b *= LINEAR_COEF;\n  }\n  b *= MAX_RGB;\n  return [\n    round ? Math.round(r) : r,\n    round ? Math.round(g) : g,\n    round ? Math.round(b) : b\n  ];\n};\n\n/**\n * convert linear rgb to hex color\n * @param {Array.<number>} rgb - [r, g, b, alpha] r|g|b|alpha: 0..1\n * @param {boolean} skip - skip validate\n * @returns {string} - hex color\n */\nexport const convertLinearRgbToHex = (\n  rgb: Array<number>,\n  skip: boolean = false\n): string => {\n  let r, g, b, alpha;\n  if (skip) {\n    [r, g, b, alpha] = rgb;\n  } else {\n    [r, g, b, alpha] = validateColorComponents(rgb, {\n      minLength: QUAT\n    });\n  }\n  [r, g, b] = convertLinearRgbToRgb([r, g, b], true);\n\n  const rr = numberToHexString(r!);\n\n  const gg = numberToHexString(g!);\n\n  const bb = numberToHexString(b!);\n  const aa = numberToHexString(alpha * MAX_RGB);\n  let hex;\n  if (aa === 'ff') {\n    hex = `#${rr}${gg}${bb}`;\n  } else {\n    hex = `#${rr}${gg}${bb}${aa}`;\n  }\n  return hex;\n};\n\n/**\n * convert xyz to hex color\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @returns {string} - hex color\n */\nexport const convertXyzToHex = (xyz: Array<number>): string => {\n  const [x, y, z, alpha] = validateColorComponents(xyz, {\n    minLength: QUAT,\n    validateRange: false\n  });\n  const [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n  const hex = convertLinearRgbToHex(\n    [\n      Math.min(Math.max(r!, 0), 1),\n      Math.min(Math.max(g!, 0), 1),\n      Math.min(Math.max(b!, 0), 1),\n      alpha\n    ],\n    true\n  );\n  return hex;\n};\n\n/**\n * convert xyz D50 to hex color\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @returns {string} - hex color\n */\nexport const convertXyzD50ToHex = (xyz: Array<number>): string => {\n  const [x, y, z, alpha] = validateColorComponents(xyz, {\n    minLength: QUAT,\n    validateRange: false\n  });\n  const xyzD65 = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n  const [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, xyzD65, true);\n  const hex = convertLinearRgbToHex([\n    Math.min(Math.max(r!, 0), 1),\n    Math.min(Math.max(g!, 0), 1),\n    Math.min(Math.max(b!, 0), 1),\n    alpha\n  ]);\n  return hex;\n};\n\n/**\n * convert xyz to rgb\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n */\nexport const convertXyzToRgb = (\n  xyz: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  let x, y, z, alpha;\n  if (skip) {\n    [x, y, z, alpha] = xyz;\n  } else {\n    [x, y, z, alpha] = validateColorComponents(xyz, {\n      validateRange: false\n    });\n  }\n  let [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n  [r, g, b] = convertLinearRgbToRgb(\n    [\n      Math.min(Math.max(r!, 0), 1),\n      Math.min(Math.max(g!, 0), 1),\n      Math.min(Math.max(b!, 0), 1)\n    ],\n    true\n  );\n  return [r, g, b, alpha];\n};\n\n/**\n * convert xyz to xyz-d50\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @returns {Array.<number>} - [x, y, z, alpha]\n */\nexport const convertXyzToXyzD50 = (xyz: Array<number>): Array<number> => {\n  const [xx, yy, zz, alpha] = validateColorComponents(xyz, {\n    validateRange: false\n  });\n  const [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [xx, yy, zz], true);\n  return [x, y, z, alpha];\n};\n\n/**\n * convert xyz to hsl\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [h, s, l, alpha]\n */\nexport const convertXyzToHsl = (\n  xyz: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  const [rr, gg, bb, alpha] = convertXyzToRgb(xyz, skip);\n  const r = rr! / MAX_RGB;\n  const g = gg! / MAX_RGB;\n  const b = bb! / MAX_RGB;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const d = max - min;\n  const l = (max + min) * HALF * MAX_PCT;\n  let h, s;\n  if (Math.round(l) === 0 || Math.round(l) === MAX_PCT) {\n    h = NONE as never;\n    s = NONE as never;\n  } else {\n    s = (d / (1 - Math.abs(max + min - 1))) * MAX_PCT;\n    if (s === 0) {\n      h = NONE as never;\n    } else {\n      switch (max) {\n        case r:\n          h = (g - b) / d;\n          break;\n        case g:\n          h = (b - r) / d + DUO;\n          break;\n        case b:\n        default:\n          h = (r - g) / d + QUAT;\n          break;\n      }\n      h = (h * SEXA) % DEG;\n      if (h < 0) {\n        h += DEG;\n      }\n    }\n  }\n  return [h, s, l, alpha!];\n};\n\n/**\n * convert xyz to hwb\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [h, w, b, alpha]\n */\nexport const convertXyzToHwb = (\n  xyz: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  const [r, g, b, alpha] = convertXyzToRgb(xyz, skip);\n  const w = Math.min(r!, g!, b!) / MAX_RGB;\n  const bk = 1 - Math.max(r!, g!, b!) / MAX_RGB;\n  let h;\n  if (w + bk === 1) {\n    h = NONE as never;\n  } else {\n    [h] = convertXyzToHsl(xyz);\n  }\n  return [h!, w * MAX_PCT, bk * MAX_PCT, alpha!];\n};\n\n/**\n * convert xyz to oklab\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [l, a, b, alpha]\n */\nexport const convertXyzToOklab = (\n  xyz: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  let x, y, z, alpha;\n  if (skip) {\n    [x, y, z, alpha] = xyz;\n  } else {\n    [x, y, z, alpha] = validateColorComponents(xyz, {\n      validateRange: false\n    });\n  }\n  const lms = transformMatrix(MATRIX_XYZ_TO_LMS, [x, y, z], true);\n  const xyzLms = lms.map((c) => Math.cbrt(c));\n  let [l, a, b] = transformMatrix(MATRIX_LMS_TO_OKLAB, xyzLms, true);\n  l = Math.min(Math.max(l!, 0), 1);\n  const lPct = Math.round(parseFloat(l.toFixed(QUAT)) * MAX_PCT);\n  if (lPct === 0 || lPct === MAX_PCT) {\n    a = NONE as never;\n    b = NONE as never;\n  }\n  return [l, a, b, alpha];\n};\n\n/**\n * convert xyz to oklch\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [l, c, h, alpha]\n */\nexport const convertXyzToOklch = (\n  xyz: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  const [l, a, b, aa] = convertXyzToOklab(xyz, skip);\n  let c, h;\n  const lPct = Math.round(parseFloat(l!.toFixed(QUAT)) * MAX_PCT);\n  if (lPct === 0 || lPct === MAX_PCT) {\n    c = NONE as never;\n    h = NONE as never;\n  } else {\n    c = Math.max(Math.sqrt(Math.pow(a!, POW_SQR) + Math.pow(b!, POW_SQR)), 0);\n    if (parseFloat(c.toFixed(QUAT)) === 0) {\n      h = NONE as never;\n    } else {\n      h = (Math.atan2(b!, a!) * DEG * HALF) / Math.PI;\n      if (h < 0) {\n        h += DEG;\n      }\n    }\n  }\n  return [l!, c, h, aa!];\n};\n\n/**\n * convert xyz D50 to rgb\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n */\nexport const convertXyzD50ToRgb = (\n  xyz: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  let x, y, z, alpha;\n  if (skip) {\n    [x, y, z, alpha] = xyz;\n  } else {\n    [x, y, z, alpha] = validateColorComponents(xyz, {\n      minLength: QUAT,\n      validateRange: false\n    });\n  }\n  const xyzD65 = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n  const [r, g, b] = convertXyzToRgb(xyzD65, true);\n  return [r, g, b, alpha];\n};\n\n/**\n * convert xyz-d50 to lab\n * @param {Array.<number>} xyz - [x, y, z, a]\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [l, a, b, alpha]\n */\nexport const convertXyzD50ToLab = (\n  xyz: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  let x, y, z, alpha;\n  if (skip) {\n    [x, y, z, alpha] = xyz;\n  } else {\n    [x, y, z, alpha] = validateColorComponents(xyz, {\n      validateRange: false\n    });\n  }\n  const xyzD50 = [x, y, z].map((val, i) => val / D50[i]!);\n  const [f0, f1, f2] = xyzD50.map((val) =>\n    val > LAB_EPSILON ? Math.cbrt(val) : (val * LAB_KAPPA + HEX) / LAB_L\n  );\n  const l = Math.min(Math.max(LAB_L * f1! - HEX, 0), MAX_PCT);\n  let a, b;\n  if (l === 0 || l === MAX_PCT) {\n    a = NONE as never;\n    b = NONE as never;\n  } else {\n    a = (f0! - f1!) * LAB_A;\n    b = (f1! - f2!) * LAB_B;\n  }\n  return [l, a, b, alpha];\n};\n\n/**\n * convert xyz-d50 to lch\n * @param {Array.<number>} xyz - [x, y, z, alpha]\n * @param {boolean} skip - skip validate\n * @returns {Array.<number>} - [l, c, h, alpha]\n */\nexport const convertXyzD50ToLch = (\n  xyz: Array<number>,\n  skip: boolean = false\n): Array<number> => {\n  const [l, a, b, alpha] = convertXyzD50ToLab(xyz, skip);\n  let c, h;\n  if (l === 0 || l === MAX_PCT) {\n    c = NONE as never;\n    h = NONE as never;\n  } else {\n    c = Math.max(Math.sqrt(Math.pow(a!, POW_SQR) + Math.pow(b!, POW_SQR)), 0);\n    h = (Math.atan2(b!, a!) * DEG * HALF) / Math.PI;\n    if (h < 0) {\n      h += DEG;\n    }\n  }\n  return [l!, c, h, alpha!];\n};\n\n/**\n * convert hex color to rgb\n * @param {string} value - color value\n * @returns {Array.<number>} - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n */\nexport const convertHexToRgb = (value: string): Array<number> => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  if (\n    !(\n      /^#[\\da-f]{6}$/.test(value) ||\n      /^#[\\da-f]{3}$/.test(value) ||\n      /^#[\\da-f]{8}$/.test(value) ||\n      /^#[\\da-f]{4}$/.test(value)\n    )\n  ) {\n    throw new SyntaxError(`Invalid property value: ${value}`);\n  }\n  const arr = [];\n  if (/^#[\\da-f]{6}$/.test(value)) {\n    const [, r, g, b] = value.match(\n      /^#([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/\n    ) as [string, string, string, string];\n    arr.push(parseInt(r, HEX), parseInt(g, HEX), parseInt(b, HEX), 1);\n  } else if (/^#[\\da-f]{3}$/.test(value)) {\n    const [, r, g, b] = value.match(/^#([\\da-f])([\\da-f])([\\da-f])$/) as [\n      string,\n      string,\n      string,\n      string\n    ];\n    arr.push(\n      parseInt(`${r}${r}`, HEX),\n      parseInt(`${g}${g}`, HEX),\n      parseInt(`${b}${b}`, HEX),\n      1\n    );\n  } else if (/^#[\\da-f]{8}$/.test(value)) {\n    const [, r, g, b, alpha] = value.match(\n      /^#([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/\n    ) as [string, string, string, string, string];\n    arr.push(\n      parseInt(r, HEX),\n      parseInt(g, HEX),\n      parseInt(b, HEX),\n      parseHexAlpha(alpha)\n    );\n  } else if (/^#[\\da-f]{4}$/.test(value)) {\n    const [, r, g, b, alpha] = value.match(\n      /^#([\\da-f])([\\da-f])([\\da-f])([\\da-f])$/\n    ) as [string, string, string, string, string];\n    arr.push(\n      parseInt(`${r}${r}`, HEX),\n      parseInt(`${g}${g}`, HEX),\n      parseInt(`${b}${b}`, HEX),\n      parseHexAlpha(`${alpha}${alpha}`)\n    );\n  }\n  return arr;\n};\n\n/**\n * convert hex color to linear rgb\n * @param {string} value - color value\n * @returns {Array.<number>} - [r, g, b, alpha] r|g|b|alpha: 0..1\n */\nexport const convertHexToLinearRgb = (value: string): Array<number> => {\n  const [rr, gg, bb, alpha] = convertHexToRgb(value);\n  const [r, g, b] = convertRgbToLinearRgb([rr!, gg!, bb!], true);\n  return [r!, g!, b!, alpha!];\n};\n\n/**\n * convert hex color to xyz\n * @param {string} value - color value\n * @returns {Array.<number>} - [x, y, z, alpha]\n */\nexport const convertHexToXyz = (value: string): Array<number> => {\n  const [r, g, b, alpha] = convertHexToLinearRgb(value);\n  const [x, y, z] = transformMatrix(MATRIX_L_RGB_TO_XYZ, [r!, g!, b!], true);\n  return [x!, y!, z!, alpha!];\n};\n\n/**\n * parse rgb()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string} - ['rgb', r, g, b, alpha], '(empty)'\n */\nexport const parseRgb = (\n  value: string,\n  opt: {\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  const reg = new RegExp(`^rgba?\\\\(\\\\s*(${SYN_MOD}|${SYN_RGB_LV3})\\\\s*\\\\)$`);\n  if (!reg.test(value)) {\n    switch (format) {\n      case VAL_MIX: {\n        return null!;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  const [, val] = value.match(reg) as [string, string];\n\n  let [v1, v2, v3, v4] = val.replace(/[,/]/g, ' ').split(/\\s+/) as [\n    string,\n    string,\n    string,\n    string\n  ];\n  let r, g, b;\n  if (v1 === NONE) {\n    r = 0;\n  } else {\n    if (v1[0] === '.') {\n      v1 = `0${v1}`;\n    }\n    if (v1.endsWith('%')) {\n      r = (parseFloat(v1) * MAX_RGB) / MAX_PCT;\n    } else {\n      r = parseFloat(v1);\n    }\n    r = Math.min(Math.max(roundToPrecision(r, OCT), 0), MAX_RGB);\n  }\n  if (v2 === NONE) {\n    g = 0;\n  } else {\n    if (v2[0] === '.') {\n      v2 = `0${v2}`;\n    }\n    if (v2.endsWith('%')) {\n      g = (parseFloat(v2) * MAX_RGB) / MAX_PCT;\n    } else {\n      g = parseFloat(v2);\n    }\n    g = Math.min(Math.max(roundToPrecision(g, OCT), 0), MAX_RGB);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else {\n    if (v3[0] === '.') {\n      v3 = `0${v3}`;\n    }\n    if (v3.endsWith('%')) {\n      b = (parseFloat(v3) * MAX_RGB) / MAX_PCT;\n    } else {\n      b = parseFloat(v3);\n    }\n    b = Math.min(Math.max(roundToPrecision(b, OCT), 0), MAX_RGB);\n  }\n  const alpha = parseAlpha(v4);\n  return ['rgb', r, g, b, format === VAL_MIX && v4 === NONE ? NONE : alpha];\n};\n\n/**\n * parse hsl()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - ['rgb', r, g, b, alpha], '(empty)', null\n */\nexport const parseHsl = (\n  value: string,\n  opt: {\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  if (!REG_HSL.test(value)) {\n    switch (format) {\n      case 'hsl':\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  const [, val] = value.match(REG_HSL) as [string, string];\n  let [h, s, l, alpha] = val.replace(/[,/]/g, ' ').split(/\\s+/) as [\n    number | string,\n    number | string,\n    number | string,\n    number | string\n  ];\n  if (h === NONE) {\n    if (format !== 'hsl') {\n      h = 0;\n    }\n  } else {\n    h = angleToDeg(h as string);\n  }\n  if (s === NONE) {\n    if (format !== 'hsl') {\n      s = 0;\n    }\n  } else {\n    if ((s as string)[0] === '.') {\n      s = `0${s}`;\n    }\n    s = Math.min(Math.max(parseFloat(s as string), 0), MAX_PCT);\n  }\n  if (l === NONE) {\n    if (format !== 'hsl') {\n      l = 0;\n    }\n  } else {\n    if ((l as string)[0] === '.') {\n      l = `0${l}`;\n    }\n    l = Math.min(Math.max(parseFloat(l as string), 0), MAX_PCT);\n  }\n  if (alpha !== NONE || format !== 'hsl') {\n    alpha = parseAlpha(alpha as string);\n  }\n  if (format === 'hsl') {\n    return [format, h, s, l, alpha];\n  }\n  const ll = (l as number) / MAX_PCT;\n  const sa = ((s as number) / MAX_PCT) * Math.min(ll, 1 - ll);\n  const rk = (((h as number) / DEG) * DOZ) % DOZ;\n  const gk = (8 + ((h as number) / DEG) * DOZ) % DOZ;\n  const bk = (4 + ((h as number) / DEG) * DOZ) % DOZ;\n  const r =\n    ll - sa * Math.max(-1, Math.min(rk - TRIA, TRIA ** POW_SQR - rk, 1));\n  const g =\n    ll - sa * Math.max(-1, Math.min(gk - TRIA, TRIA ** POW_SQR - gk, 1));\n  const b =\n    ll - sa * Math.max(-1, Math.min(bk - TRIA, TRIA ** POW_SQR - bk, 1));\n  return [\n    'rgb',\n    Math.min(Math.max(roundToPrecision(r * MAX_RGB, OCT), 0), MAX_RGB),\n    Math.min(Math.max(roundToPrecision(g * MAX_RGB, OCT), 0), MAX_RGB),\n    Math.min(Math.max(roundToPrecision(b * MAX_RGB, OCT), 0), MAX_RGB),\n    alpha\n  ];\n};\n\n/**\n * parse hwb()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - ['rgb', r, g, b, alpha], '(empty)', null\n */\nexport const parseHwb = (\n  value: string,\n  opt: {\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  if (!REG_HWB.test(value)) {\n    switch (format) {\n      case 'hwb':\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  const [, val] = value.match(REG_HWB) as [string, string];\n  let [h, w, b, alpha] = val.replace('/', ' ').split(/\\s+/) as [\n    number | string,\n    number | string,\n    number | string,\n    number | string\n  ];\n  if (h === NONE) {\n    if (format !== 'hwb') {\n      h = 0;\n    }\n  } else {\n    h = angleToDeg(h as string);\n  }\n  if (w === NONE) {\n    if (format !== 'hwb') {\n      w = 0;\n    }\n  } else {\n    if ((w as string)[0] === '.') {\n      w = `0${w}`;\n    }\n    w = Math.min(Math.max(parseFloat(w as string), 0), MAX_PCT) / MAX_PCT;\n  }\n  if (b === NONE) {\n    if (format !== 'hwb') {\n      b = 0;\n    }\n  } else {\n    if ((b as string)[0] === '.') {\n      b = `0${b}`;\n    }\n    b = Math.min(Math.max(parseFloat(b as string), 0), MAX_PCT) / MAX_PCT;\n  }\n  if (alpha !== NONE || format !== 'hwb') {\n    alpha = parseAlpha(alpha as string);\n  }\n  if (format === 'hwb') {\n    return [\n      format,\n      h,\n      w === NONE ? w : w * MAX_PCT,\n      b === NONE ? b : b * MAX_PCT,\n      alpha\n    ];\n  }\n  if ((w as number) + (b as number) >= 1) {\n    const v = roundToPrecision(\n      ((w as number) / ((w as number) + (b as number))) * MAX_RGB,\n      OCT\n    );\n    return ['rgb', v, v, v, alpha];\n  }\n  const factor = (1 - (w as number) - (b as number)) / MAX_RGB;\n  let [, rr, gg, bb] = parseHsl(`hsl(${h} 100 50)`) as [\n    string,\n    number | string,\n    number | string,\n    number | string\n  ];\n  rr = roundToPrecision(\n    ((rr as number) * factor + (w as number)) * MAX_RGB,\n    OCT\n  );\n  gg = roundToPrecision(\n    ((gg as number) * factor + (w as number)) * MAX_RGB,\n    OCT\n  );\n  bb = roundToPrecision(\n    ((bb as number) * factor + (w as number)) * MAX_RGB,\n    OCT\n  );\n  return [\n    'rgb',\n    Math.min(Math.max(rr, 0), MAX_RGB),\n    Math.min(Math.max(gg, 0), MAX_RGB),\n    Math.min(Math.max(bb, 0), MAX_RGB),\n    alpha\n  ];\n};\n\n/**\n * parse lab()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - [xyz-d50, x, y, z, alpha], ['lab', l, a, b, alpha], '(empty)', null\n */\nexport const parseLab = (\n  value: string,\n  opt: {\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  if (!REG_LAB.test(value)) {\n    switch (format) {\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  const COEF_PCT = 1.25;\n  const COND_POW = 8;\n  const [, val] = value.match(REG_LAB) as [string, string];\n  let [l, a, b, alpha] = val.replace('/', ' ').split(/\\s+/) as [\n    number | string,\n    number | string,\n    number | string,\n    number | string\n  ];\n  if (l === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      l = 0;\n    }\n  } else {\n    if ((l as string)[0] === '.') {\n      l = `0${l}`;\n    }\n    if ((l as string).endsWith('%')) {\n      l = parseFloat(l as string);\n      if (l > MAX_PCT) {\n        l = MAX_PCT;\n      }\n    } else {\n      l = parseFloat(l as string);\n    }\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (a === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      a = 0;\n    }\n  } else {\n    if ((a as string)[0] === '.') {\n      a = `0${a}`;\n    }\n    if ((a as string).endsWith('%')) {\n      a = parseFloat(a as string) * COEF_PCT;\n    } else {\n      a = parseFloat(a as string);\n    }\n  }\n  if (b === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      b = 0;\n    }\n  } else {\n    if ((b as string).endsWith('%')) {\n      b = parseFloat(b as string) * COEF_PCT;\n    } else {\n      b = parseFloat(b as string);\n    }\n  }\n  if (alpha !== NONE || !REG_SPEC.test(format!)) {\n    alpha = parseAlpha(alpha as string);\n  }\n  if (REG_SPEC.test(format!)) {\n    return [\n      'lab',\n      l === NONE ? l : roundToPrecision(l, HEX),\n      a === NONE ? a : roundToPrecision(a, HEX),\n      b === NONE ? b : roundToPrecision(b, HEX),\n      alpha\n    ];\n  }\n  const fl = ((l as number) + HEX) / LAB_L;\n  const fa = (a as number) / LAB_A + fl;\n  const fb = fl - (b as number) / LAB_B;\n  const powFl = Math.pow(fl, POW_CUBE);\n  const powFa = Math.pow(fa, POW_CUBE);\n  const powFb = Math.pow(fb, POW_CUBE);\n  const xyz = [\n    powFa > LAB_EPSILON ? powFa : (fa * LAB_L - HEX) / LAB_KAPPA,\n    (l as number) > COND_POW ? powFl : (l as number) / LAB_KAPPA,\n    powFb > LAB_EPSILON ? powFb : (fb * LAB_L - HEX) / LAB_KAPPA\n  ];\n  const [x, y, z] = xyz.map((val, i) => val * D50[i]!);\n  return [\n    'xyz-d50',\n    roundToPrecision(x!, HEX),\n    roundToPrecision(y!, HEX),\n    roundToPrecision(z!, HEX),\n    alpha\n  ];\n};\n\n/**\n * parse lch()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - ['xyz-d50', x, y, z, alpha], ['lch', l, c, h, alpha], '(empty)', null\n */\nexport const parseLch = (\n  value: string,\n  opt: {\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  if (!REG_LCH.test(value)) {\n    switch (format) {\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  const COEF_PCT = 1.5;\n  const [, val] = value.match(REG_LCH) as [string, string];\n  let [l, c, h, alpha] = val.replace('/', ' ').split(/\\s+/) as [\n    number | string,\n    number | string,\n    number | string,\n    number | string\n  ];\n  if (l === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      l = 0;\n    }\n  } else {\n    if ((l as string)[0] === '.') {\n      l = `0${l}`;\n    }\n    l = parseFloat(l as string);\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (c === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      c = 0;\n    }\n  } else {\n    if ((c as string)[0] === '.') {\n      c = `0${c}`;\n    }\n    if ((c as string).endsWith('%')) {\n      c = parseFloat(c as string) * COEF_PCT;\n    } else {\n      c = parseFloat(c as string);\n    }\n  }\n  if (h === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      h = 0;\n    }\n  } else {\n    h = angleToDeg(h as string);\n  }\n  if (alpha !== NONE || !REG_SPEC.test(format!)) {\n    alpha = parseAlpha(alpha as string);\n  }\n  if (REG_SPEC.test(format!)) {\n    return [\n      'lch',\n      l === NONE ? l : roundToPrecision(l, HEX),\n      c === NONE ? c : roundToPrecision(c, HEX),\n      h === NONE ? h : roundToPrecision(h, HEX),\n      alpha\n    ];\n  }\n  const a = (c as number) * Math.cos(((h as number) * Math.PI) / (DEG * HALF));\n  const b = (c as number) * Math.sin(((h as number) * Math.PI) / (DEG * HALF));\n  const [, x, y, z] = parseLab(`lab(${l} ${a} ${b})`) as [\n    string,\n    number | string,\n    number | string,\n    number | string\n  ];\n  return [\n    'xyz-d50',\n    roundToPrecision(x as number, HEX),\n    roundToPrecision(y as number, HEX),\n    roundToPrecision(z as number, HEX),\n    alpha\n  ];\n};\n\n/**\n * parse oklab()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - ['xyz-d65', x, y, z, alpha], ['oklab', l, a, b, alpha], '(empty)', null\n */\nexport const parseOklab = (\n  value: string,\n  opt: {\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  if (!REG_OKLAB.test(value)) {\n    switch (format) {\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  const COEF_PCT = 0.4;\n  const [, val] = value.match(REG_OKLAB) as [string, string];\n  let [l, a, b, alpha] = val.replace('/', ' ').split(/\\s+/) as [\n    number | string,\n    number | string,\n    number | string,\n    number | string\n  ];\n  if (l === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      l = 0;\n    }\n  } else {\n    if ((l as string)[0] === '.') {\n      l = `0${l}`;\n    }\n    if ((l as string).endsWith('%')) {\n      l = parseFloat(l as string) / MAX_PCT;\n    } else {\n      l = parseFloat(l as string);\n    }\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (a === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      a = 0;\n    }\n  } else {\n    if ((a as string)[0] === '.') {\n      a = `0${a}`;\n    }\n    if ((a as string).endsWith('%')) {\n      a = (parseFloat(a as string) * COEF_PCT) / MAX_PCT;\n    } else {\n      a = parseFloat(a as string);\n    }\n  }\n  if (b === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      b = 0;\n    }\n  } else {\n    if ((b as string).endsWith('%')) {\n      b = (parseFloat(b as string) * COEF_PCT) / MAX_PCT;\n    } else {\n      b = parseFloat(b as string);\n    }\n  }\n  if (alpha !== NONE || !REG_SPEC.test(format!)) {\n    alpha = parseAlpha(alpha as string);\n  }\n  if (REG_SPEC.test(format!)) {\n    return [\n      'oklab',\n      l === NONE ? l : roundToPrecision(l, HEX),\n      a === NONE ? a : roundToPrecision(a, HEX),\n      b === NONE ? b : roundToPrecision(b, HEX),\n      alpha\n    ];\n  }\n  const lms = transformMatrix(MATRIX_OKLAB_TO_LMS, [\n    l as number,\n    a as number,\n    b as number\n  ]);\n  const xyzLms = lms.map((c) => Math.pow(c, POW_CUBE));\n  const [x, y, z] = transformMatrix(MATRIX_LMS_TO_XYZ, xyzLms, true);\n  return [\n    'xyz-d65',\n    roundToPrecision(x!, HEX),\n    roundToPrecision(y!, HEX),\n    roundToPrecision(z!, HEX),\n    alpha\n  ];\n};\n\n/**\n * parse oklch()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - ['xyz-d65', x, y, z, alpha], ['oklch', l, c, h, alpha], '(empty)', null\n */\nexport const parseOklch = (\n  value: string,\n  opt: {\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  if (!REG_OKLCH.test(value)) {\n    switch (format) {\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  const COEF_PCT = 0.4;\n  const [, val] = value.match(REG_OKLCH) as [string, string];\n  let [l, c, h, alpha] = val.replace('/', ' ').split(/\\s+/) as [\n    number | string,\n    number | string,\n    number | string,\n    number | string\n  ];\n  if (l === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      l = 0;\n    }\n  } else {\n    if ((l as string)[0] === '.') {\n      l = `0${l}`;\n    }\n    if ((l as string).endsWith('%')) {\n      l = parseFloat(l as string) / MAX_PCT;\n    } else {\n      l = parseFloat(l as string);\n    }\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (c === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      c = 0;\n    }\n  } else {\n    if ((c as string)[0] === '.') {\n      c = `0${c}`;\n    }\n    if ((c as string).endsWith('%')) {\n      c = (parseFloat(c as string) * COEF_PCT) / MAX_PCT;\n    } else {\n      c = parseFloat(c as string);\n    }\n    if (c < 0) {\n      c = 0;\n    }\n  }\n  if (h === NONE) {\n    if (!REG_SPEC.test(format!)) {\n      h = 0;\n    }\n  } else {\n    h = angleToDeg(h as string);\n  }\n  if (alpha !== NONE || !REG_SPEC.test(format!)) {\n    alpha = parseAlpha(alpha as string);\n  }\n  if (REG_SPEC.test(format!)) {\n    return [\n      'oklch',\n      l === NONE ? l : roundToPrecision(l, HEX),\n      c === NONE ? c : roundToPrecision(c, HEX),\n      h === NONE ? h : roundToPrecision(h, HEX),\n      alpha\n    ];\n  }\n  const a = (c as number) * Math.cos(((h as number) * Math.PI) / (DEG * HALF));\n  const b = (c as number) * Math.sin(((h as number) * Math.PI) / (DEG * HALF));\n  const lms = transformMatrix(MATRIX_OKLAB_TO_LMS, [l as number, a, b]);\n  const xyzLms = lms.map((cl) => Math.pow(cl, POW_CUBE));\n  const [x, y, z] = transformMatrix(MATRIX_LMS_TO_XYZ, xyzLms, true);\n  return [\n    'xyz-d65',\n    roundToPrecision(x!, HEX),\n    roundToPrecision(y!, HEX),\n    roundToPrecision(z!, HEX),\n    alpha\n  ];\n};\n\n/**\n * parse color()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.colorSpace] - color space\n * @param {boolean} [opt.d50] - xyz in d50 white point\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - ['xyz-(d50|d65)', x, y, z, alpha], [cs, r, g, b, alpha], '(empty)', null\n */\nexport const parseColorFunc = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    d50?: boolean;\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace, d50, format } = opt;\n  if (!REG_FN_COLOR.test(value)) {\n    switch (format) {\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  const [, val] = value.match(REG_FN_COLOR) as [string, string];\n  let [cs, v1, v2, v3, v4] = val.replace('/', ' ').split(/\\s+/) as [\n    string,\n    string,\n    string,\n    string,\n    string\n  ];\n  let r, g, b;\n  if (cs === 'xyz') {\n    cs = 'xyz-d65';\n  }\n  if (v1 === NONE) {\n    r = 0;\n  } else {\n    if (v1[0] === '.') {\n      v1 = `0${v1}`;\n    }\n    r = v1.endsWith('%') ? parseFloat(v1) / MAX_PCT : parseFloat(v1);\n  }\n  if (v2 === NONE) {\n    g = 0;\n  } else {\n    if (v2[0] === '.') {\n      v2 = `0${v2}`;\n    }\n    g = v2.endsWith('%') ? parseFloat(v2) / MAX_PCT : parseFloat(v2);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else {\n    if (v3[0] === '.') {\n      v3 = `0${v3}`;\n    }\n    b = v3.endsWith('%') ? parseFloat(v3) / MAX_PCT : parseFloat(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format!) || (format === VAL_MIX && cs === colorSpace)) {\n    return [\n      cs,\n      v1 === NONE ? NONE : roundToPrecision(r, DEC),\n      v2 === NONE ? NONE : roundToPrecision(g, DEC),\n      v3 === NONE ? NONE : roundToPrecision(b, DEC),\n      v4 === NONE ? NONE : alpha\n    ];\n  }\n  let x, y, z;\n  // srgb\n  if (cs === 'srgb') {\n    [x, y, z] = convertRgbToXyz([r * MAX_RGB, g * MAX_RGB, b * MAX_RGB]);\n    if (d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D65_TO_D50,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // srgb-linear\n  } else if (cs === 'srgb-linear') {\n    [x, y, z] = transformMatrix(MATRIX_L_RGB_TO_XYZ, [r, g, b]);\n    if (d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D65_TO_D50,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // display-p3\n  } else if (cs === 'display-p3') {\n    const linearRgb = convertRgbToLinearRgb([\n      r * MAX_RGB,\n      g * MAX_RGB,\n      b * MAX_RGB\n    ]);\n    [x, y, z] = transformMatrix(MATRIX_P3_TO_XYZ, linearRgb);\n    if (d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D65_TO_D50,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // rec2020\n  } else if (cs === 'rec2020') {\n    const ALPHA = 1.09929682680944;\n    const BETA = 0.018053968510807;\n    const REC_COEF = 0.45;\n    const rgb = [r, g, b].map((c) => {\n      let cl;\n      if (c < BETA * REC_COEF * DEC) {\n        cl = c / (REC_COEF * DEC);\n      } else {\n        cl = Math.pow((c + ALPHA - 1) / ALPHA, 1 / REC_COEF);\n      }\n      return cl;\n    });\n    [x, y, z] = transformMatrix(MATRIX_REC2020_TO_XYZ, rgb);\n    if (d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D65_TO_D50,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // a98-rgb\n  } else if (cs === 'a98-rgb') {\n    const POW_A98 = 563 / 256;\n    const rgb = [r, g, b].map((c) => {\n      const cl = Math.pow(c, POW_A98);\n      return cl;\n    });\n    [x, y, z] = transformMatrix(MATRIX_A98_TO_XYZ, rgb);\n    if (d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D65_TO_D50,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // prophoto-rgb\n  } else if (cs === 'prophoto-rgb') {\n    const POW_PROPHOTO = 1.8;\n    const rgb = [r, g, b].map((c) => {\n      let cl;\n      if (c > 1 / (HEX * DUO)) {\n        cl = Math.pow(c, POW_PROPHOTO);\n      } else {\n        cl = c / HEX;\n      }\n      return cl;\n    });\n    [x, y, z] = transformMatrix(MATRIX_PROPHOTO_TO_XYZ_D50, rgb);\n    if (!d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D50_TO_D65,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // xyz, xyz-d50, xyz-d65\n  } else if (/^xyz(?:-d(?:50|65))?$/.test(cs)) {\n    [x, y, z] = [r, g, b];\n    if (cs === 'xyz-d50') {\n      if (!d50) {\n        [x, y, z] = transformMatrix(MATRIX_D50_TO_D65, [x, y, z]);\n      }\n    } else if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n  }\n  return [\n    d50 ? 'xyz-d50' : 'xyz-d65',\n    roundToPrecision(x!, HEX),\n    roundToPrecision(y!, HEX),\n    roundToPrecision(z!, HEX),\n    format === VAL_MIX && v4 === NONE ? NONE : alpha\n  ];\n};\n\n/**\n * parse color value\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {boolean} [opt.d50] - xyz in d50 white point\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - ['xyz-(d50|d65)', x, y, z, alpha], ['rgb', r, g, b, alpha]\n *   - value, '(empty)', null\n */\nexport const parseColorValue = (\n  value: string,\n  opt: {\n    d50?: boolean;\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { d50, format } = opt;\n  // unknown color and/or invalid color\n  if (!REG_COLOR.test(value)) {\n    switch (format) {\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  let x, y, z, alpha;\n  // complement currentcolor as a missing color\n  if (REG_CURRENT.test(value)) {\n    if (format === VAL_COMP) {\n      return ['rgb', 0, 0, 0, 0];\n    }\n    if (format === VAL_SPEC) {\n      return value;\n    }\n    x = 0;\n    y = 0;\n    z = 0;\n    alpha = 0;\n    // named-color\n  } else if (/^[a-z]+$/.test(value)) {\n    if (Object.prototype.hasOwnProperty.call(NAMED_COLORS, value)) {\n      if (format === VAL_SPEC) {\n        return value;\n      }\n      const [r, g, b] = NAMED_COLORS[value as keyof typeof NAMED_COLORS] as [\n        number,\n        number,\n        number\n      ];\n      alpha = 1;\n      if (format === VAL_COMP) {\n        return ['rgb', r, g, b, alpha];\n      }\n      [x, y, z] = convertRgbToXyz([r, g, b], true);\n      if (d50) {\n        [x, y, z] = transformMatrix(\n          MATRIX_D65_TO_D50,\n          [x as number, y as number, z as number],\n          true\n        );\n      }\n    } else {\n      if (format === VAL_COMP) {\n        return ['rgb', 0, 0, 0, 0];\n      }\n      if (format === VAL_SPEC) {\n        if (value === 'transparent') {\n          return value;\n        }\n        return '';\n      }\n      if (format === VAL_MIX) {\n        if (value === 'transparent') {\n          return ['rgb', 0, 0, 0, 0];\n        }\n        return null;\n      }\n      x = 0;\n      y = 0;\n      z = 0;\n      alpha = 0;\n    }\n    // hex-color\n  } else if (value[0] === '#') {\n    if (REG_SPEC.test(format!)) {\n      const rgb = convertHexToRgb(value);\n      return ['rgb', ...rgb];\n    }\n    [x, y, z, alpha] = convertHexToXyz(value);\n    if (d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D65_TO_D50,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // lab()\n  } else if (value.startsWith('lab')) {\n    if (REG_SPEC.test(format!)) {\n      return parseLab(value, opt);\n    }\n    [, x, y, z, alpha] = parseLab(value) as [\n      string,\n      number | string,\n      number | string,\n      number | string,\n      number | string\n    ];\n    if (!d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D50_TO_D65,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // lch()\n  } else if (value.startsWith('lch')) {\n    if (REG_SPEC.test(format!)) {\n      return parseLch(value, opt);\n    }\n    [, x, y, z, alpha] = parseLch(value) as [\n      string,\n      number | string,\n      number | string,\n      number | string,\n      number | string\n    ];\n    if (!d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D50_TO_D65,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // oklab()\n  } else if (value.startsWith('oklab')) {\n    if (REG_SPEC.test(format!)) {\n      return parseOklab(value, opt);\n    }\n    [, x, y, z, alpha] = parseOklab(value) as [\n      string,\n      number | string,\n      number | string,\n      number | string,\n      number | string\n    ];\n    if (d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D65_TO_D50,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n    // oklch()\n  } else if (value.startsWith('oklch')) {\n    if (REG_SPEC.test(format!)) {\n      return parseOklch(value, opt);\n    }\n    [, x, y, z, alpha] = parseOklch(value) as [\n      string,\n      number | string,\n      number | string,\n      number | string,\n      number | string\n    ];\n    if (d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D65_TO_D50,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n  } else {\n    let r, g, b;\n    // hsl()\n    if (value.startsWith('hsl')) {\n      [, r, g, b, alpha] = parseHsl(value) as [\n        string,\n        number | string,\n        number | string,\n        number | string,\n        number | string\n      ];\n      // hwb()\n    } else if (value.startsWith('hwb')) {\n      [, r, g, b, alpha] = parseHwb(value) as [\n        string,\n        number | string,\n        number | string,\n        number | string,\n        number | string\n      ];\n      // rgb()\n    } else {\n      [, r, g, b, alpha] = parseRgb(value, opt) as [\n        string,\n        number | string,\n        number | string,\n        number | string,\n        number | string\n      ];\n    }\n    if (REG_SPEC.test(format!)) {\n      return [\n        'rgb',\n        Math.round(r as number),\n        Math.round(g as number),\n        Math.round(b as number),\n        alpha\n      ];\n    }\n    [x, y, z] = convertRgbToXyz([r as number, g as number, b as number]);\n    if (d50) {\n      [x, y, z] = transformMatrix(\n        MATRIX_D65_TO_D50,\n        [x as number, y as number, z as number],\n        true\n      );\n    }\n  }\n  return [\n    d50 ? 'xyz-d50' : 'xyz-d65',\n    roundToPrecision(x as number, HEX),\n    roundToPrecision(y as number, HEX),\n    roundToPrecision(z as number, HEX),\n    alpha!\n  ];\n};\n\n/**\n * resolve color value\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.colorSpace] - color space\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - [cs, v1, v2, v3, alpha], value, '(empty)', null\n */\nexport const resolveColorValue = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace, format } = opt;\n  // unknown color and/or invalid color\n  if (!REG_COLOR.test(value)) {\n    switch (format) {\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  let cs, r, g, b, alpha;\n  // complement currentcolor as a missing color\n  if (REG_CURRENT.test(value)) {\n    if (format === VAL_SPEC) {\n      return value;\n    }\n    r = 0;\n    g = 0;\n    b = 0;\n    alpha = 0;\n    // named-color\n  } else if (/^[a-z]+$/.test(value)) {\n    if (Object.prototype.hasOwnProperty.call(NAMED_COLORS, value)) {\n      if (format === VAL_SPEC) {\n        return value;\n      }\n      [r, g, b] = NAMED_COLORS[value as keyof typeof NAMED_COLORS] as [\n        number,\n        number,\n        number\n      ];\n      alpha = 1;\n    } else {\n      if (format === VAL_SPEC) {\n        if (value === 'transparent') {\n          return value;\n        }\n        return '';\n      }\n      if (format === VAL_MIX) {\n        if (value === 'transparent') {\n          return ['rgb', 0, 0, 0, 0];\n        }\n        return null;\n      }\n      r = 0;\n      g = 0;\n      b = 0;\n      alpha = 0;\n    }\n    // hex-color\n  } else if (value[0] === '#') {\n    [r, g, b, alpha] = convertHexToRgb(value) as [\n      number,\n      number,\n      number,\n      number\n    ];\n    // rgb()\n  } else if (value.startsWith('rgb')) {\n    [, r, g, b, alpha] = parseRgb(value, opt) as [\n      string,\n      number | string,\n      number | string,\n      number | string,\n      number | string\n    ];\n    // hsl()\n  } else if (value.startsWith('hsl')) {\n    [, r, g, b, alpha] = parseHsl(value, opt) as [\n      string,\n      number | string,\n      number | string,\n      number | string,\n      number | string\n    ];\n    // hwb()\n  } else if (value.startsWith('hwb')) {\n    [, r, g, b, alpha] = parseHwb(value, opt) as [\n      string,\n      number | string,\n      number | string,\n      number | string,\n      number | string\n    ];\n    // lab(), lch()\n  } else if (/^l(?:ab|ch)/.test(value)) {\n    let x, y, z;\n    if (value.startsWith('lab')) {\n      [cs, x, y, z, alpha] = parseLab(value, opt) as [\n        string,\n        number | string,\n        number | string,\n        number | string,\n        number | string\n      ];\n    } else {\n      [cs, x, y, z, alpha] = parseLch(value, opt) as [\n        string,\n        number | string,\n        number | string,\n        number | string,\n        number | string\n      ];\n    }\n    if (REG_SPEC.test(format!)) {\n      return [cs, x, y, z, alpha];\n    }\n    [r, g, b, alpha] = convertXyzD50ToRgb([\n      x as number,\n      y as number,\n      z as number,\n      alpha as number\n    ]);\n    // oklab(), oklch()\n  } else if (/^okl(?:ab|ch)/.test(value)) {\n    let x, y, z;\n    if (value.startsWith('oklab')) {\n      [cs, x, y, z, alpha] = parseOklab(value, opt) as [\n        string,\n        number | string,\n        number | string,\n        number | string,\n        number | string\n      ];\n    } else {\n      [cs, x, y, z, alpha] = parseOklch(value, opt) as [\n        string,\n        number | string,\n        number | string,\n        number | string,\n        number | string\n      ];\n    }\n    if (REG_SPEC.test(format!)) {\n      return [cs, x, y, z, alpha];\n    }\n    [r, g, b, alpha] = convertXyzToRgb([\n      x as number,\n      y as number,\n      z as number,\n      alpha as number\n    ]);\n  }\n  if (format === VAL_MIX && colorSpace === 'srgb') {\n    return [\n      'srgb',\n      (r as number) / MAX_RGB,\n      (g as number) / MAX_RGB,\n      (b as number) / MAX_RGB,\n      alpha!\n    ];\n  }\n  return [\n    'rgb',\n    Math.round(r as number),\n    Math.round(g as number),\n    Math.round(b as number),\n    alpha!\n  ];\n};\n\n/**\n * resolve color()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.colorSpace] - color space\n * @param {string} [opt.format] - output format\n * @returns {Array.<string|number>|?string}\n *   - [cs, v1, v2, v3, alpha], '(empty)', null\n */\nexport const resolveColorFunc = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number | string> | string | null => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace, format } = opt;\n  if (!REG_FN_COLOR.test(value)) {\n    switch (format) {\n      case VAL_MIX: {\n        return null;\n      }\n      case VAL_SPEC: {\n        return '';\n      }\n      default: {\n        return ['rgb', 0, 0, 0, 0];\n      }\n    }\n  }\n  const [cs, x, y, z, alpha] = parseColorFunc(value, opt) as [\n    string,\n    number | string,\n    number | string,\n    number | string,\n    number | string\n  ];\n  if (REG_SPEC.test(format!) ||\n      (format === VAL_MIX && cs === colorSpace)) {\n    return [cs, x, y, z, alpha];\n  }\n  const [r, g, b] = convertXyzToRgb([\n    x as number,\n    y as number,\n    z as number\n  ], true) as [number, number, number];\n  return ['rgb', r, g, b, alpha as number];\n};\n\n/**\n * convert color value to linear rgb\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.colorSpace] - color space\n * @param {string} [opt.format] - output format\n * @returns {Array.<number>} - [r, g, b, alpha] r|g|b|alpha: 0..1\n */\nexport const convertColorToLinearRgb = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace, format } = opt;\n  let cs, r, g, b, alpha, x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz === null) {\n      return xyz!;\n    }\n    [cs, x, y, z, alpha] = xyz as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n    if (cs === colorSpace) {\n      return [x, y, z, alpha];\n    }\n    [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true) as [\n      number,\n      number,\n      number\n    ];\n  } else if (value.startsWith(FN_COLOR)) {\n    const [, val] = value.match(REG_FN_COLOR) as [string, string];\n    const [cs] = val.replace('/', ' ').split(/\\s+/);\n    if (cs === 'srgb-linear') {\n      [, r, g, b, alpha] = resolveColorFunc(value, {\n        format: VAL_COMP\n      }) as [string, number, number, number, number];\n    } else {\n      [, x, y, z, alpha] = parseColorFunc(value) as [\n        string,\n        number,\n        number,\n        number,\n        number\n      ];\n      [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true) as [\n        number,\n        number,\n        number\n      ];\n    }\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n    [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true) as [\n      number,\n      number,\n      number\n    ];\n  }\n  return [\n    Math.min(Math.max(r, 0), 1),\n    Math.min(Math.max(g, 0), 1),\n    Math.min(Math.max(b, 0), 1),\n    alpha\n  ];\n};\n\n/**\n * convert color value to rgb\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<number>} - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n */\nexport const convertColorToRgb = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  let r, g, b, alpha;\n  if (format === VAL_MIX) {\n    let rgb;\n    if (value.startsWith(FN_COLOR)) {\n      rgb = resolveColorFunc(value, opt);\n    } else {\n      rgb = resolveColorValue(value, opt);\n    }\n    if (rgb === null) {\n      return rgb!;\n    }\n    [, r, g, b, alpha] = rgb as [string, number, number, number, number];\n  } else if (value.startsWith(FN_COLOR)) {\n    const [, val] = value.match(REG_FN_COLOR) as [string, string];\n    const [cs] = val.replace('/', ' ').split(/\\s+/);\n    if (cs === 'srgb') {\n      [, r, g, b, alpha] = resolveColorFunc(value, {\n        format: VAL_COMP\n      }) as [string, number, number, number, number];\n      r *= MAX_RGB;\n      g *= MAX_RGB;\n      b *= MAX_RGB;\n    } else {\n      [, r, g, b, alpha] = resolveColorFunc(value) as [\n        string,\n        number,\n        number,\n        number,\n        number\n      ];\n    }\n  } else if (/^(?:ok)?l(?:ab|ch)/.test(value)) {\n    [r, g, b, alpha] = convertColorToLinearRgb(value) as [\n      number,\n      number,\n      number,\n      number\n    ];\n    [r, g, b] = convertLinearRgbToRgb([r, g, b]) as [number, number, number];\n  } else {\n    [, r, g, b, alpha] = resolveColorValue(value, {\n      format: VAL_COMP\n    }) as [string, number, number, number, number];\n  }\n  return [r, g, b, alpha];\n};\n\n/**\n * convert color value to xyz\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {boolean} [opt.d50] - xyz in d50 white point\n * @param {string} [opt.format] - output format\n * @returns {Array.<number>} - [x, y, z, alpha]\n */\nexport const convertColorToXyz = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    d50?: boolean;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { d50, format } = opt;\n  let x, y, z, alpha;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz === null) {\n      return xyz!;\n    }\n    [, x, y, z, alpha] = xyz as [string, number, number, number, number];\n  } else if (value.startsWith(FN_COLOR)) {\n    const [, val] = value.match(REG_FN_COLOR) as [string, string];\n    const [cs] = val.replace('/', ' ').split(/\\s+/);\n    if (d50) {\n      if (cs === 'xyz-d50') {\n        [, x, y, z, alpha] = resolveColorFunc(value, {\n          format: VAL_COMP\n        }) as [string, number, number, number, number];\n      } else {\n        [, x, y, z, alpha] = parseColorFunc(value, opt) as [\n          string,\n          number,\n          number,\n          number,\n          number\n        ];\n      }\n    } else if (/^xyz(?:-d65)?$/.test(cs!)) {\n      [, x, y, z, alpha] = resolveColorFunc(value, {\n        format: VAL_COMP\n      }) as [string, number, number, number, number];\n    } else {\n      [, x, y, z, alpha] = parseColorFunc(value) as [\n        string,\n        number,\n        number,\n        number,\n        number\n      ];\n    }\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value, opt) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n  return [x, y, z, alpha];\n};\n\n/**\n * convert color value to hsl\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<number>} - [h, s, l, alpha]\n */\nexport const convertColorToHsl = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  let h, s, l, alpha, x, y, z;\n  if (REG_HSL.test(value)) {\n    [, h, s, l, alpha] = parseHsl(value, {\n      format: 'hsl'\n    }) as [string, number, number, number, number];\n    if (format === 'hsl') {\n      return [\n        Math.round(h as number),\n        Math.round(s as number),\n        Math.round(l as number),\n        alpha as number\n      ];\n    }\n    return [h, s, l, alpha];\n  }\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz === null) {\n      return xyz!;\n    }\n    [, x, y, z, alpha] = xyz as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n  [h, s, l] = convertXyzToHsl([x, y, z], true) as [number, number, number];\n  if (format === 'hsl') {\n    return [Math.round(h), Math.round(s), Math.round(l), alpha];\n  }\n  return [h, s, l, alpha];\n};\n\n/**\n * convert color value to hwb\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<number>} - [h, w, b, alpha]\n */\nexport const convertColorToHwb = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  let h, w, b, alpha, x, y, z;\n  if (REG_HWB.test(value)) {\n    [, h, w, b, alpha] = parseHwb(value, {\n      format: 'hwb'\n    }) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n    if (format === 'hwb') {\n      return [Math.round(h), Math.round(w), Math.round(b), alpha];\n    }\n    return [h, w, b, alpha];\n  }\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz === null) {\n      return xyz!;\n    }\n    [, x, y, z, alpha] = xyz as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n  [h, w, b] = convertXyzToHwb([x, y, z], true) as [number, number, number];\n  if (format === 'hwb') {\n    return [Math.round(h), Math.round(w), Math.round(b), alpha];\n  }\n  return [h, w, b, alpha];\n};\n\n/**\n * convert color value to lab\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<number>} - [l, a, b, alpha]\n */\nexport const convertColorToLab = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  let l, a, b, alpha, x, y, z;\n  if (REG_LAB.test(value)) {\n    [, l, a, b, alpha] = parseLab(value, {\n      format: VAL_COMP\n    }) as [string, number, number, number, number];\n    return [l, a, b, alpha];\n  }\n  if (format === VAL_MIX) {\n    let xyz;\n    (opt as Record<string, boolean>).d50 = true;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz === null) {\n      return xyz!;\n    }\n    [, x, y, z, alpha] = xyz as [string, number, number, number, number];\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value, {\n      d50: true\n    }) as [string, number, number, number, number];\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value, {\n      d50: true\n    }) as [string, number, number, number, number];\n  }\n  [l, a, b] = convertXyzD50ToLab([x, y, z], true) as [number, number, number];\n  return [l, a, b, alpha];\n};\n\n/**\n * convert color value to lch\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<number>} - [l, c, h, alpha]\n */\nexport const convertColorToLch = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  let l, c, h, alpha, x, y, z;\n  if (REG_LCH.test(value)) {\n    [, l, c, h, alpha] = parseLch(value, {\n      format: VAL_COMP\n    }) as [string, number, number, number, number];\n    return [l, c, h, alpha];\n  }\n  if (format === VAL_MIX) {\n    let xyz;\n    (opt as Record<string, boolean>).d50 = true;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz === null) {\n      return xyz!;\n    }\n    [, x, y, z, alpha] = xyz as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value, {\n      d50: true\n    }) as [string, number, number, number, number];\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value, {\n      d50: true\n    }) as [string, number, number, number, number];\n  }\n  [l, c, h] = convertXyzD50ToLch([x, y, z], true) as [number, number, number];\n  return [l, c, h, alpha];\n};\n\n/**\n * convert color value to oklab\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<number>} - [l, a, b, alpha]\n */\nexport const convertColorToOklab = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  let l, a, b, alpha, x, y, z;\n  if (REG_OKLAB.test(value)) {\n    [, l, a, b, alpha] = parseOklab(value, {\n      format: VAL_COMP\n    }) as [string, number, number, number, number];\n    return [l, a, b, alpha];\n  }\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz === null) {\n      return xyz!;\n    }\n    [, x, y, z, alpha] = xyz as [string, number, number, number, number];\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n  [l, a, b] = convertXyzToOklab([x, y, z], true) as [number, number, number];\n  return [l, a, b, alpha];\n};\n\n/**\n * convert color value to oklch\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] - output format\n * @returns {Array.<number>} - [l, c, h, alpha]\n */\nexport const convertColorToOklch = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): Array<number> => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  let l, c, h, alpha, x, y, z;\n  if (REG_OKLCH.test(value)) {\n    [, l, c, h, alpha] = parseOklch(value, {\n      format: VAL_COMP\n    }) as [string, number, number, number, number];\n    return [l, c, h, alpha];\n  }\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz === null) {\n      return xyz!;\n    }\n    [, x, y, z, alpha] = xyz as [string, number, number, number, number];\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as [\n      string,\n      number,\n      number,\n      number,\n      number\n    ];\n  }\n  [l, c, h] = convertXyzToOklch([x, y, z], true) as [number, number, number];\n  return [l, c, h, alpha];\n};\n\n/**\n * resolve color-mix()\n * @param {string} value - color value\n * @param {object} [opt] - options\n * @param {string} [opt.format] format - output format\n * @returns {Array.<string|number>|?string}\n *   - [cs, v1, v2, v3, alpha], '(empty)', null\n */\nexport const resolveColorMix = (\n  value: string,\n  opt: {\n    format?: string;\n  } = {}\n): Array<number | string> | (string | null) => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format } = opt;\n  const nestedItems = [] as Array<string>;\n  if (!REG_MIX.test(value)) {\n    if (value.startsWith(FN_MIX) && REG_MIX_NEST.test(value)) {\n      const regColorSpace = new RegExp(`^(?:${CS_RGB}|${CS_XYZ})$`);\n      const items = value.match(REG_MIX_NEST)!;\n      for (const item of items) {\n        let val = resolveColorMix(item, {\n          format: format === VAL_SPEC ? format : VAL_COMP\n        }) as [string, number, number, number, number] | string;\n        // computed value\n        if (Array.isArray(val)) {\n          const [v1, v2, v3, v4, v5] = val as [\n            string,\n            number,\n            number,\n            number,\n            number\n          ];\n          if (v2 === 0 && v3 === 0 && v4 === 0 && v5 === 0) {\n            value = '';\n            break;\n          }\n          if (regColorSpace.test(v1)) {\n            if (v5 === 1) {\n              val = `color(${v1} ${v2} ${v3} ${v4})`;\n            } else {\n              val = `color(${v1} ${v2} ${v3} ${v4} / ${v5})`;\n            }\n          } else if (v5 === 1) {\n            val = `${v1}(${v2} ${v3} ${v4})`;\n          } else {\n            val = `${v1}(${v2} ${v3} ${v4} / ${v5})`;\n          }\n        } else if (!REG_MIX.test(val)) {\n          value = '';\n          break;\n        }\n        nestedItems.push(val);\n        value = value.replace(item, val);\n      }\n      if (!value) {\n        if (format === VAL_SPEC) {\n          return '';\n        }\n        return ['rgb', 0, 0, 0, 0];\n      }\n    } else if (format === VAL_SPEC) {\n      return '';\n    } else {\n      return ['rgb', 0, 0, 0, 0];\n    }\n  }\n  let colorSpace, hueArc, colorA, pctA, colorB, pctB;\n  if (nestedItems.length && format === VAL_SPEC) {\n    const regColorSpace = new RegExp(`^color-mix\\\\(\\\\s*in\\\\s+(${CS_MIX})\\\\s*,`);\n    const [, cs] = value.match(regColorSpace) as [string, string];\n    if (REG_CS_HUE.test(cs)) {\n      [, colorSpace, hueArc] = cs.match(REG_CS_HUE) as [\n        string,\n        string,\n        string\n      ];\n    } else {\n      colorSpace = cs;\n    }\n    if (nestedItems.length === 2) {\n      const itemA = nestedItems[0]!.replace(/(?=[()])/g, '\\\\');\n      const regA = new RegExp(`(${itemA})(?:\\\\s+(${PCT}))?`);\n      //if (regA.test(value)) {\n        [, colorA, pctA] = value.match(regA) as [string, string, string];\n      //}\n      const itemB = nestedItems[1]!.replace(/(?=[()])/g, '\\\\');\n      const regB = new RegExp(`(${itemB})(?:\\\\s+(${PCT}))?`);\n      //if (regB.test(value)) {\n        [, colorB, pctB] = value.match(regB) as [string, string, string];\n      //}\n    } else {\n      const colorPart = `(?:${SYN_COLOR_TYPE})(?:\\\\s+${PCT})?`;\n      const item = nestedItems[0]!.replace(/(?=[()])/g, '\\\\');\n      const itemPart = `${item}(?:\\\\s+${PCT})?`;\n      const itemPartCapt = `(${item})(?:\\\\s+(${PCT}))?`;\n      const regColorPart = new RegExp(`^(${SYN_COLOR_TYPE})(?:\\\\s+(${PCT}))?$`);\n      const regItemPart = new RegExp(`^${itemPartCapt}$`);\n      const regPosition = new RegExp(`${itemPartCapt}\\\\s*\\\\)$`);\n      // item is at the end\n      if (regPosition.test(value)) {\n        const reg = new RegExp(`(${colorPart})\\\\s*,\\\\s*(${itemPart})\\\\s*\\\\)$`);\n        const [, colorPartA, colorPartB] = value.match(reg) as [\n          string,\n          string,\n          string\n        ];\n        [, colorA, pctA] = colorPartA.match(regColorPart) as [\n          string,\n          string,\n          string\n        ];\n        [, colorB, pctB] = colorPartB.match(regItemPart) as [\n          string,\n          string,\n          string\n        ];\n      } else {\n        const reg = new RegExp(`(${itemPart})\\\\s*,\\\\s*(${colorPart})\\\\s*\\\\)$`);\n        const [, colorPartA, colorPartB] = value.match(reg) as [\n          string,\n          string,\n          string\n        ];\n        [, colorA, pctA] = colorPartA.match(regItemPart) as [\n          string,\n          string,\n          string\n        ];\n        [, colorB, pctB] = colorPartB.match(regColorPart) as [\n          string,\n          string,\n          string\n        ];\n      }\n    }\n  } else {\n    const [, cs, colorPartA, colorPartB] = value.match(REG_MIX_CAPT) as [\n      string,\n      string,\n      string,\n      string\n    ];\n    const reg = new RegExp(`^(${SYN_COLOR_TYPE})(?:\\\\s+(${PCT}))?$`);\n    [, colorA, pctA] = colorPartA.match(reg) as [string, string, string];\n    [, colorB, pctB] = colorPartB.match(reg) as [string, string, string];\n    if (REG_CS_HUE.test(cs)) {\n      [, colorSpace, hueArc] = REG_CS_HUE.exec(cs) as unknown as [\n        string,\n        string,\n        string\n      ];\n    } else {\n      colorSpace = cs;\n    }\n  }\n  // normalize percentages and set multipler\n  let pA, pB, m;\n  if (pctA && pctB) {\n    const p1 = parseFloat(pctA) / MAX_PCT;\n    const p2 = parseFloat(pctB) / MAX_PCT;\n    if (p1 < 0 || p1 > 1 || p2 < 0 || p2 > 1) {\n      if (format === VAL_SPEC) {\n        return '';\n      }\n      return ['rgb', 0, 0, 0, 0];\n    }\n    const factor = p1 + p2;\n    if (factor === 0) {\n      if (format === VAL_SPEC) {\n        return '';\n      }\n      return ['rgb', 0, 0, 0, 0];\n    }\n    pA = p1 / factor;\n    pB = p2 / factor;\n    m = factor < 1 ? factor : 1;\n  } else {\n    if (pctA) {\n      pA = parseFloat(pctA) / MAX_PCT;\n      if (pA < 0 || pA > 1) {\n        if (format === VAL_SPEC) {\n          return '';\n        }\n        return ['rgb', 0, 0, 0, 0];\n      }\n      pB = 1 - pA;\n    } else if (pctB) {\n      pB = parseFloat(pctB) / MAX_PCT;\n      if (pB < 0 || pB > 1) {\n        if (format === VAL_SPEC) {\n          return '';\n        }\n        return ['rgb', 0, 0, 0, 0];\n      }\n      pA = 1 - pB;\n    } else {\n      pA = HALF;\n      pB = HALF;\n    }\n    m = 1;\n  }\n  if (colorSpace === 'xyz') {\n    colorSpace = 'xyz-d65';\n  }\n  // specified value\n  if (format === VAL_SPEC) {\n    let valueA, valueB;\n    if (colorA.startsWith(FN_MIX)) {\n      valueA = colorA;\n    } else if (colorA.startsWith(FN_COLOR)) {\n      valueA = parseColorFunc(colorA, opt);\n      if (Array.isArray(valueA)) {\n        const [v1, v2, v3, v4, v5] = [...valueA];\n        if (v5 === 1) {\n          valueA = `color(${v1} ${v2} ${v3} ${v4})`;\n        } else {\n          valueA = `color(${v1} ${v2} ${v3} ${v4} / ${v5})`;\n        }\n      }\n    } else {\n      valueA = parseColorValue(colorA, opt);\n      if (valueA === '') {\n        return valueA;\n      }\n      if (Array.isArray(valueA)) {\n        const [v1, v2, v3, v4, v5] = [...valueA];\n        if (v5 === 1) {\n          if (v1 === 'rgb') {\n            valueA = `${v1}(${v2}, ${v3}, ${v4})`;\n          } else {\n            valueA = `${v1}(${v2} ${v3} ${v4})`;\n          }\n        } else if (v1 === 'rgb') {\n          valueA = `${v1}a(${v2}, ${v3}, ${v4}, ${v5})`;\n        } else {\n          valueA = `${v1}(${v2} ${v3} ${v4} / ${v5})`;\n        }\n      }\n    }\n    if (colorB!.startsWith(FN_MIX)) {\n      valueB = colorB;\n    } else if (colorB!.startsWith(FN_COLOR)) {\n      valueB = parseColorFunc(colorB!, opt);\n      if (Array.isArray(valueB)) {\n        const [v1, v2, v3, v4, v5] = [...valueB];\n        if (v5 === 1) {\n          valueB = `color(${v1} ${v2} ${v3} ${v4})`;\n        } else {\n          valueB = `color(${v1} ${v2} ${v3} ${v4} / ${v5})`;\n        }\n      }\n    } else {\n      valueB = parseColorValue(colorB!, opt);\n      if (valueB === '') {\n        return valueB;\n      }\n      if (Array.isArray(valueB)) {\n        const [v1, v2, v3, v4, v5] = [...valueB];\n        if (v5 === 1) {\n          if (v1 === 'rgb') {\n            valueB = `${v1}(${v2}, ${v3}, ${v4})`;\n          } else {\n            valueB = `${v1}(${v2} ${v3} ${v4})`;\n          }\n        } else if (v1 === 'rgb') {\n          valueB = `${v1}a(${v2}, ${v3}, ${v4}, ${v5})`;\n        } else {\n          valueB = `${v1}(${v2} ${v3} ${v4} / ${v5})`;\n        }\n      }\n    }\n    if (pctA && pctB) {\n      valueA += ` ${parseFloat(pctA)}%`;\n      valueB += ` ${parseFloat(pctB)}%`;\n    } else if (pctA) {\n      const pA = parseFloat(pctA);\n      if (pA !== MAX_PCT * HALF) {\n        valueA += ` ${pA}%`;\n      }\n    } else if (pctB) {\n      const pA = MAX_PCT - parseFloat(pctB);\n      if (pA !== MAX_PCT * HALF) {\n        valueA += ` ${pA}%`;\n      }\n    }\n    if (hueArc) {\n      return `color-mix(in ${colorSpace} ${hueArc} hue, ${valueA}, ${valueB})`;\n    } else {\n      return `color-mix(in ${colorSpace}, ${valueA}, ${valueB})`;\n    }\n  }\n  let r, g, b, alpha;\n  // in srgb, srgb-linear\n  if (/^srgb(?:-linear)?$/.test(colorSpace)) {\n    let rgbA, rgbB;\n    if (colorSpace === 'srgb') {\n      if (REG_CURRENT.test(colorA)) {\n        rgbA = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbA = convertColorToRgb(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB!)) {\n        rgbB = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbB = convertColorToRgb(colorB!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA!)) {\n        rgbA = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbA = convertColorToLinearRgb(colorA!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB!)) {\n        rgbB = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbB = convertColorToLinearRgb(colorB!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (rgbA === null || rgbB === null) {\n      return ['rgb', 0, 0, 0, 0];\n    }\n    let [rA, gA, bA, alphaA] = rgbA as [\n      number | string,\n      number | string,\n      number | string,\n      number | string\n    ];\n    let [rB, gB, bB, alphaB] = rgbB as [\n      number | string,\n      number | string,\n      number | string,\n      number | string\n    ];\n    const rNone = rA === NONE && rB === NONE;\n    const gNone = gA === NONE && gB === NONE;\n    const bNone = bA === NONE && bB === NONE;\n    const alphaNone = alphaA === NONE && alphaB === NONE;\n    [[rA, gA, bA, alphaA], [rB, gB, bB, alphaB]] = normalizeColorComponents(\n      [rA, gA, bA, alphaA],\n      [rB, gB, bB, alphaB],\n      true\n    ) as [[number, number, number, number], [number, number, number, number]];\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    if (alpha === 0) {\n      r = rA * pA + rB * pB;\n      g = gA * pA + gB * pB;\n      b = bA * pA + bB * pB;\n    } else {\n      r = (rA * factorA + rB * factorB) / alpha;\n      g = (gA * factorA + gB * factorB) / alpha;\n      b = (bA * factorA + bB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      return [\n        colorSpace,\n        rNone ? NONE : roundToPrecision(r, HEX),\n        gNone ? NONE : roundToPrecision(g, HEX),\n        bNone ? NONE : roundToPrecision(b, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n    }\n    r *= MAX_RGB;\n    g *= MAX_RGB;\n    b *= MAX_RGB;\n    // in xyz, xyz-d65, xyz-d50\n  } else if (REG_CS_XYZ.test(colorSpace)) {\n    let xyzA, xyzB;\n    if (REG_CURRENT.test(colorA!)) {\n      xyzA = [NONE, NONE, NONE, NONE];\n    } else {\n      xyzA = convertColorToXyz(colorA!, {\n        colorSpace,\n        d50: colorSpace === 'xyz-d50',\n        format: VAL_MIX\n      });\n    }\n    if (REG_CURRENT.test(colorB!)) {\n      xyzB = [NONE, NONE, NONE, NONE];\n    } else {\n      xyzB = convertColorToXyz(colorB!, {\n        colorSpace,\n        d50: colorSpace === 'xyz-d50',\n        format: VAL_MIX\n      });\n    }\n    if (xyzA === null || xyzB === null) {\n      return ['rgb', 0, 0, 0, 0];\n    }\n    let [xA, yA, zA, alphaA] = xyzA;\n    let [xB, yB, zB, alphaB] = xyzB;\n    const xNone = xA === NONE && xB === NONE;\n    const yNone = yA === NONE && yB === NONE;\n    const zNone = zA === NONE && zB === NONE;\n    const alphaNone = alphaA === NONE && alphaB === NONE;\n    [[xA, yA, zA, alphaA], [xB, yB, zB, alphaB]] = normalizeColorComponents(\n      [xA, yA, zA, alphaA],\n      [xB, yB, zB, alphaB],\n      true\n    ) as [[number, number, number, number], [number, number, number, number]];\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    let x, y, z;\n    if (alpha === 0) {\n      x = xA * pA + xB * pB;\n      y = yA * pA + yB * pB;\n      z = zA * pA + zB * pB;\n    } else {\n      x = (xA * factorA + xB * factorB) / alpha;\n      y = (yA * factorA + yB * factorB) / alpha;\n      z = (zA * factorA + zB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      return [\n        colorSpace,\n        xNone ? NONE : roundToPrecision(x, HEX),\n        yNone ? NONE : roundToPrecision(y, HEX),\n        zNone ? NONE : roundToPrecision(z, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n    }\n    if (colorSpace === 'xyz-d50') {\n      [r, g, b] = convertXyzD50ToRgb([x, y, z], true);\n    } else {\n      [r, g, b] = convertXyzToRgb([x, y, z], true);\n    }\n    // in hsl, hwb\n  } else if (/^h(?:sl|wb)$/.test(colorSpace)) {\n    let hslA, hslB;\n    if (colorSpace === 'hsl') {\n      if (REG_CURRENT.test(colorA!)) {\n        hslA = [NONE, NONE, NONE, NONE];\n      } else {\n        hslA = convertColorToHsl(colorA!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB!)) {\n        hslB = [NONE, NONE, NONE, NONE];\n      } else {\n        hslB = convertColorToHsl(colorB!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA!)) {\n        hslA = [NONE, NONE, NONE, NONE];\n      } else {\n        hslA = convertColorToHwb(colorA!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB!)) {\n        hslB = [NONE, NONE, NONE, NONE];\n      } else {\n        hslB = convertColorToHwb(colorB!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (hslA === null || hslB === null) {\n      return ['rgb', 0, 0, 0, 0];\n    }\n    let [hA, sA, lA, alphaA] = hslA;\n    let [hB, sB, lB, alphaB] = hslB;\n    const alphaNone = alphaA === NONE && alphaB === NONE;\n    [[hA, sA, lA, alphaA], [hB, sB, lB, alphaB]] = normalizeColorComponents(\n      [hA, sA, lA, alphaA],\n      [hB, sB, lB, alphaB],\n      true\n    ) as [[number, number, number, number], [number, number, number, number]];\n    if (hueArc) {\n      [hA, hB] = interpolateHue(hA, hB, hueArc);\n    }\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    const h = ((hA as number) * pA + (hB as number) * pB) % DEG;\n    let s, l;\n    if (alpha === 0) {\n      s = sA * pA + sB * pB;\n      l = lA * pA + lB * pB;\n    } else {\n      s = (sA * factorA + sB * factorB) / alpha;\n      l = (lA * factorA + lB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    [r, g, b] = convertColorToRgb(`${colorSpace}(${h} ${s} ${l})`) as [\n      number,\n      number,\n      number\n    ];\n    if (format === VAL_COMP) {\n      return [\n        'srgb',\n        roundToPrecision(r / MAX_RGB, HEX),\n        roundToPrecision(g / MAX_RGB, HEX),\n        roundToPrecision(b / MAX_RGB, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n    }\n    // in lab, oklab\n  } else if (/^(?:ok)?lab$/.test(colorSpace)) {\n    let labA, labB;\n    if (colorSpace === 'lab') {\n      if (REG_CURRENT.test(colorA!)) {\n        labA = [NONE, NONE, NONE, NONE];\n      } else {\n        labA = convertColorToLab(colorA!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB!)) {\n        labB = [NONE, NONE, NONE, NONE];\n      } else {\n        labB = convertColorToLab(colorB!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA!)) {\n        labA = [NONE, NONE, NONE, NONE];\n      } else {\n        labA = convertColorToOklab(colorA!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB!)) {\n        labB = [NONE, NONE, NONE, NONE];\n      } else {\n        labB = convertColorToOklab(colorB!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (labA === null || labB === null) {\n      return ['rgb', 0, 0, 0, 0];\n    }\n    let [lA, aA, bA, alphaA] = labA;\n    let [lB, aB, bB, alphaB] = labB;\n    const lNone = lA === NONE && lB === NONE;\n    const aNone = aA === NONE && aB === NONE;\n    const bNone = bA === NONE && bB === NONE;\n    const alphaNone = alphaA === NONE && alphaB === NONE;\n    [[lA, aA, bA, alphaA], [lB, aB, bB, alphaB]] = normalizeColorComponents(\n      [lA, aA, bA, alphaA],\n      [lB, aB, bB, alphaB],\n      true\n    ) as [[number, number, number, number], [number, number, number, number]];\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    let l, aO, bO;\n    if (alpha === 0) {\n      l = lA * pA + lB * pB;\n      aO = aA * pA + aB * pB;\n      bO = bA * pA + bB * pB;\n    } else {\n      l = (lA * factorA + lB * factorB) / alpha;\n      aO = (aA * factorA + aB * factorB) / alpha;\n      bO = (bA * factorA + bB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      return [\n        colorSpace,\n        lNone ? NONE : roundToPrecision(l, HEX),\n        aNone ? NONE : roundToPrecision(aO, HEX),\n        bNone ? NONE : roundToPrecision(bO, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n    }\n    [, r, g, b] = resolveColorValue(`${colorSpace}(${l} ${aO} ${bO})`) as [\n      string,\n      number,\n      number,\n      number\n    ];\n    // in lch, oklch\n  } else if (/^(?:ok)?lch$/.test(colorSpace)) {\n    let lchA, lchB;\n    if (colorSpace === 'lch') {\n      if (REG_CURRENT.test(colorA!)) {\n        lchA = [NONE, NONE, NONE, NONE];\n      } else {\n        lchA = convertColorToLch(colorA!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB!)) {\n        lchB = [NONE, NONE, NONE, NONE];\n      } else {\n        lchB = convertColorToLch(colorB!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA!)) {\n        lchA = [NONE, NONE, NONE, NONE];\n      } else {\n        lchA = convertColorToOklch(colorA!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB!)) {\n        lchB = [NONE, NONE, NONE, NONE];\n      } else {\n        lchB = convertColorToOklch(colorB!, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (lchA === null || lchB === null) {\n      return ['rgb', 0, 0, 0, 0];\n    }\n    let [lA, cA, hA, alphaA] = lchA;\n    let [lB, cB, hB, alphaB] = lchB;\n    const lNone = lA === NONE && lB === NONE;\n    const cNone = cA === NONE && cB === NONE;\n    const hNone = hA === NONE && hB === NONE;\n    const alphaNone = alphaA === NONE && alphaB === NONE;\n    [[lA, cA, hA, alphaA], [lB, cB, hB, alphaB]] = normalizeColorComponents(\n      [lA, cA, hA, alphaA],\n      [lB, cB, hB, alphaB],\n      true\n    ) as [[number, number, number, number], [number, number, number, number]];\n    if (hueArc) {\n      [hA, hB] = interpolateHue(hA, hB, hueArc) as [number, number];\n    }\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    const h = (hA * pA + hB * pB) % DEG;\n    let l, c;\n    if (alpha === 0) {\n      l = lA * pA + lB * pB;\n      c = cA * pA + cB * pB;\n    } else {\n      l = (lA * factorA + lB * factorB) / alpha;\n      c = (cA * factorA + cB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      return [\n        colorSpace,\n        lNone ? NONE : roundToPrecision(l, HEX),\n        cNone ? NONE : roundToPrecision(c, HEX),\n        hNone ? NONE : roundToPrecision(h, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n    }\n    [, r, g, b] = resolveColorValue(`${colorSpace}(${l} ${c} ${h})`) as [\n      string,\n      number,\n      number,\n      number\n    ];\n  }\n  return [\n    'rgb',\n    Math.round(r!),\n    Math.round(g!),\n    Math.round(b!),\n    parseFloat((alpha! * m).toFixed(3))\n  ];\n};\n"], "names": ["val", "cs", "pA"], "mappings": ";;;AAoCA,MAAM,UAAU;AAGhB,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,gBAAgB;AACtB,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,cAAc,MAAM;AAC1B,MAAM,YAAY,QAAQ;AAG1B,MAAM,MAAM,CAAC,SAAS,QAAQ,IAAM,IAAM,SAAS,UAAU,MAAM;AACnE,MAAM,oBAAoB;AAAA,EACxB,CAAC,mBAAmB,sBAAsB,mBAAmB;AAAA,EAC7D,CAAC,qBAAqB,oBAAoB,oBAAoB;AAAA,EAC9D,CAAC,sBAAsB,uBAAuB,iBAAiB;AACjE;AACA,MAAM,oBAAoB;AAAA,EACxB,CAAC,oBAAoB,sBAAsB,oBAAoB;AAAA,EAC/D,CAAC,qBAAqB,oBAAoB,qBAAqB;AAAA,EAC/D,CAAC,uBAAuB,sBAAsB,kBAAkB;AAClE;AAGA,MAAM,sBAAsB;AAAA,EAC1B,CAAC,SAAS,SAAS,QAAQ,QAAQ,QAAQ,KAAK;AAAA,EAChD,CAAC,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,MAAM;AAAA,EAChD,CAAC,OAAO,QAAQ,QAAQ,QAAQ,UAAU,OAAO;AACnD;AACA,MAAM,sBAAsB;AAAA,EAC1B,CAAC,QAAQ,MAAM,OAAO,KAAK,QAAQ,IAAI;AAAA,EACvC,CAAC,UAAU,QAAQ,UAAU,QAAQ,QAAQ,MAAM;AAAA,EACnD,CAAC,MAAM,OAAO,QAAQ,OAAO,MAAM,GAAG;AACxC;AACA,MAAM,oBAAoB;AAAA,EACxB,CAAC,mBAAmB,oBAAoB,mBAAmB;AAAA,EAC3D,CAAC,oBAAoB,oBAAoB,kBAAkB;AAAA,EAC3D,CAAC,oBAAoB,oBAAoB,kBAAkB;AAC7D;AACA,MAAM,oBAAoB;AAAA,EACxB,CAAC,oBAAoB,qBAAqB,kBAAkB;AAAA,EAC5D,CAAC,qBAAqB,mBAAmB,mBAAmB;AAAA,EAC5D,CAAC,qBAAqB,qBAAqB,kBAAkB;AAC/D;AACA,MAAM,sBAAsB;AAAA,EAC1B,CAAC,GAAK,oBAAoB,kBAAkB;AAAA,EAC5C,CAAC,GAAK,qBAAqB,mBAAmB;AAAA,EAC9C,CAAC,GAAK,qBAAqB,mBAAmB;AAChD;AACA,MAAM,sBAAsB;AAAA,EAC1B,CAAC,mBAAmB,oBAAoB,mBAAmB;AAAA,EAC3D,CAAC,oBAAoB,mBAAqB,iBAAiB;AAAA,EAC3D,CAAC,oBAAoB,oBAAoB,mBAAmB;AAC9D;AACA,MAAM,mBAAmB;AAAA,EACvB,CAAC,SAAS,SAAS,SAAS,QAAQ,SAAS,OAAO;AAAA,EACpD,CAAC,QAAQ,QAAQ,SAAS,QAAQ,SAAS,OAAO;AAAA,EAClD,CAAC,IAAI,GAAG,QAAQ,QAAQ,UAAU,OAAO;AAC3C;AACA,MAAM,wBAAwB;AAAA,EAC5B,CAAC,WAAW,UAAU,WAAW,WAAW,WAAW,SAAS;AAAA,EAChE,CAAC,WAAW,UAAU,YAAY,WAAW,UAAU,SAAS;AAAA,EAChE,CAAC,IAAI,GAAG,WAAW,WAAW,YAAY,SAAS;AACrD;AACA,MAAM,oBAAoB;AAAA,EACxB,CAAC,SAAS,QAAQ,SAAS,SAAS,SAAS,MAAM;AAAA,EACnD,CAAC,SAAS,SAAS,UAAU,SAAS,SAAS,OAAO;AAAA,EACtD,CAAC,QAAQ,SAAS,SAAS,SAAS,UAAU,OAAO;AACvD;AACA,MAAM,6BAA6B;AAAA,EACjC,CAAC,oBAAoB,qBAAqB,kBAAkB;AAAA,EAE5D,CAAC,oBAAoB,mBAAmB,iBAAmB;AAAA,EAE3D,CAAC,GAAK,GAAK,kBAAkB;AAC/B;AAGA,MAAM,YAAY,IAAI,OAAO,OAAO,cAAc,IAAI;AACtD,MAAM,aAAa,IAAI,OAAO,IAAI,WAAW,GAAG;AAChD,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,eAAe,IAAI,OAAO,iBAAiB,YAAY,WAAW;AACxE,MAAM,UAAU,IAAI,OAAO,iBAAiB,OAAO,IAAI,WAAW,WAAW;AAC7E,MAAM,UAAU,IAAI,OAAO,eAAe,OAAO,WAAW;AAC5D,MAAM,UAAU,IAAI,OAAO,eAAe,OAAO,WAAW;AAC5D,MAAM,UAAU,IAAI,OAAO,eAAe,OAAO,WAAW;AAC5D,MAAM,UAAU,IAAI,OAAO,IAAI,OAAO,GAAG;AACzC,MAAM,eAAe,IAAI,OAAO,IAAI,YAAY,GAAG;AACnD,MAAM,eAAe,IAAI,OAAO,GAAG,OAAO,IAAI,GAAG;AACjD,MAAM,YAAY,IAAI,OAAO,iBAAiB,OAAO,WAAW;AAChE,MAAM,YAAY,IAAI,OAAO,iBAAiB,OAAO,WAAW;AAChE,MAAM,WAAW;AAGV,MAAM,eAAe;AAAA,EAC1B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,MAAM,CAAC,GAAM,KAAM,GAAI;AAAA,EACvB,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,OAAO,CAAC,GAAM,GAAM,CAAI;AAAA,EACxB,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,MAAM,CAAC,GAAM,GAAM,GAAI;AAAA,EACvB,YAAY,CAAC,KAAM,IAAM,GAAI;AAAA,EAC7B,OAAO,CAAC,KAAM,IAAM,EAAI;AAAA,EACxB,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,CAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,EAAI;AAAA,EAC5B,OAAO,CAAC,KAAM,KAAM,EAAI;AAAA,EACxB,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,SAAS,CAAC,KAAM,IAAM,EAAI;AAAA,EAC1B,MAAM,CAAC,GAAM,KAAM,GAAI;AAAA,EACvB,UAAU,CAAC,GAAM,GAAM,GAAI;AAAA,EAC3B,UAAU,CAAC,GAAM,KAAM,GAAI;AAAA,EAC3B,eAAe,CAAC,KAAM,KAAM,EAAI;AAAA,EAChC,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,WAAW,CAAC,GAAM,KAAM,CAAI;AAAA,EAC5B,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,GAAM,GAAI;AAAA,EAC9B,gBAAgB,CAAC,IAAM,KAAM,EAAI;AAAA,EACjC,YAAY,CAAC,KAAM,KAAM,CAAI;AAAA,EAC7B,YAAY,CAAC,KAAM,IAAM,GAAI;AAAA,EAC7B,SAAS,CAAC,KAAM,GAAM,CAAI;AAAA,EAC1B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,eAAe,CAAC,IAAM,IAAM,GAAI;AAAA,EAChC,eAAe,CAAC,IAAM,IAAM,EAAI;AAAA,EAChC,eAAe,CAAC,IAAM,IAAM,EAAI;AAAA,EAChC,eAAe,CAAC,GAAM,KAAM,GAAI;AAAA,EAChC,YAAY,CAAC,KAAM,GAAM,GAAI;AAAA,EAC7B,UAAU,CAAC,KAAM,IAAM,GAAI;AAAA,EAC3B,aAAa,CAAC,GAAM,KAAM,GAAI;AAAA,EAC9B,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,YAAY,CAAC,IAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,IAAM,EAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,aAAa,CAAC,IAAM,KAAM,EAAI;AAAA,EAC9B,SAAS,CAAC,KAAM,GAAM,GAAI;AAAA,EAC1B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,MAAM,CAAC,KAAM,KAAM,CAAI;AAAA,EACvB,WAAW,CAAC,KAAM,KAAM,EAAI;AAAA,EAC5B,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,OAAO,CAAC,GAAM,KAAM,CAAI;AAAA,EACxB,aAAa,CAAC,KAAM,KAAM,EAAI;AAAA,EAC9B,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,WAAW,CAAC,KAAM,IAAM,EAAI;AAAA,EAC5B,QAAQ,CAAC,IAAM,GAAM,GAAI;AAAA,EACzB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,WAAW,CAAC,KAAM,KAAM,CAAI;AAAA,EAC5B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,sBAAsB,CAAC,KAAM,KAAM,GAAI;AAAA,EACvC,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,eAAe,CAAC,IAAM,KAAM,GAAI;AAAA,EAChC,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,MAAM,CAAC,GAAM,KAAM,CAAI;AAAA,EACvB,WAAW,CAAC,IAAM,KAAM,EAAI;AAAA,EAC5B,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,SAAS,CAAC,KAAM,GAAM,GAAI;AAAA,EAC1B,QAAQ,CAAC,KAAM,GAAM,CAAI;AAAA,EACzB,kBAAkB,CAAC,KAAM,KAAM,GAAI;AAAA,EACnC,YAAY,CAAC,GAAM,GAAM,GAAI;AAAA,EAC7B,cAAc,CAAC,KAAM,IAAM,GAAI;AAAA,EAC/B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,gBAAgB,CAAC,IAAM,KAAM,GAAI;AAAA,EACjC,iBAAiB,CAAC,KAAM,KAAM,GAAI;AAAA,EAClC,mBAAmB,CAAC,GAAM,KAAM,GAAI;AAAA,EACpC,iBAAiB,CAAC,IAAM,KAAM,GAAI;AAAA,EAClC,iBAAiB,CAAC,KAAM,IAAM,GAAI;AAAA,EAClC,cAAc,CAAC,IAAM,IAAM,GAAI;AAAA,EAC/B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,MAAM,CAAC,GAAM,GAAM,GAAI;AAAA,EACvB,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,OAAO,CAAC,KAAM,KAAM,CAAI;AAAA,EACxB,WAAW,CAAC,KAAM,KAAM,EAAI;AAAA,EAC5B,QAAQ,CAAC,KAAM,KAAM,CAAI;AAAA,EACzB,WAAW,CAAC,KAAM,IAAM,CAAI;AAAA,EAC5B,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,MAAM,CAAC,KAAM,KAAM,EAAI;AAAA,EACvB,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,QAAQ,CAAC,KAAM,GAAM,GAAI;AAAA,EACzB,eAAe,CAAC,KAAM,IAAM,GAAI;AAAA,EAChC,KAAK,CAAC,KAAM,GAAM,CAAI;AAAA,EACtB,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,IAAM,EAAI;AAAA,EAC9B,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,YAAY,CAAC,KAAM,KAAM,EAAI;AAAA,EAC7B,UAAU,CAAC,IAAM,KAAM,EAAI;AAAA,EAC3B,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,QAAQ,CAAC,KAAM,IAAM,EAAI;AAAA,EACzB,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,WAAW,CAAC,KAAM,IAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,aAAa,CAAC,GAAM,KAAM,GAAI;AAAA,EAC9B,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,KAAK,CAAC,KAAM,KAAM,GAAI;AAAA,EACtB,MAAM,CAAC,GAAM,KAAM,GAAI;AAAA,EACvB,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,QAAQ,CAAC,KAAM,IAAM,EAAI;AAAA,EACzB,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,QAAQ,CAAC,KAAM,KAAM,CAAI;AAAA,EACzB,aAAa,CAAC,KAAM,KAAM,EAAI;AAChC;AAcO,MAAM,0BAA0B,CACrC,KACA,MAOI,OACW;AACf,MAAI,CAAC,MAAM,QAAQ,GAAG,GAAG;AACvB,UAAM,IAAI,UAAU,GAAG,GAAG,mBAAmB;AAAA,EAAA;AAEzC,QAAA;AAAA,IACJ,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,gBAAgB;AAAA,EAAA,IACd;AACJ,MAAI,CAAC,OAAO,SAAS,SAAS,GAAG;AAC/B,UAAM,IAAI,UAAU,GAAG,SAAS,mBAAmB;AAAA,EAAA;AAErD,MAAI,CAAC,OAAO,SAAS,SAAS,GAAG;AAC/B,UAAM,IAAI,UAAU,GAAG,SAAS,mBAAmB;AAAA,EAAA;AAErD,MAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC9B,UAAM,IAAI,UAAU,GAAG,QAAQ,mBAAmB;AAAA,EAAA;AAEpD,MAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC9B,UAAM,IAAI,UAAU,GAAG,QAAQ,mBAAmB;AAAA,EAAA;AAEpD,QAAM,IAAI,IAAI;AACV,MAAA,IAAI,aAAa,IAAI,WAAW;AAClC,UAAM,IAAI,MAAM,2BAA2B,CAAC,GAAG;AAAA,EAAA;AAEjD,MAAI,IAAI;AACR,SAAO,IAAI,GAAG;AACN,UAAA,IAAI,IAAI,CAAC;AACf,QAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,YAAM,IAAI,UAAU,GAAG,CAAC,mBAAmB;AAAA,IAAA,WAClC,IAAI,QAAQ,kBAAkB,IAAI,YAAY,IAAI,WAAW;AAChE,YAAA,IAAI,WAAW,GAAG,CAAC,mBAAmB,QAAQ,QAAQ,QAAQ,GAAG;AAAA,IAAA,WAC9D,MAAM,SAAS,IAAI,KAAK,IAAI,IAAI;AACzC,YAAM,IAAI,WAAW,GAAG,CAAC,0BAA0B;AAAA,IAAA;AAErD;AAAA,EAAA;AAEE,MAAA,SAAS,MAAM,MAAM;AACvB,QAAI,KAAK,CAAC;AAAA,EAAA;AAEL,SAAA;AACT;AASO,MAAM,kBAAkB,CAC7B,KACA,KACA,OAAgB,UACE;AAClB,MAAI,CAAC,MAAM,QAAQ,GAAG,GAAG;AACvB,UAAM,IAAI,UAAU,GAAG,GAAG,mBAAmB;AAAA,EAAA,WACpC,IAAI,WAAW,MAAM;AAC9B,UAAM,IAAI,MAAM,2BAA2B,IAAI,MAAM,GAAG;AAAA,EAAA,WAC/C,CAAC,MAAM;AAChB,aAAS,KAAK,KAAK;AACjB,UAAI,wBAAwB,GAAG;AAAA,QAC7B,WAAW;AAAA,QACX,eAAe;AAAA,MAAA,CAChB;AAAA,IAAA;AAAA,EACH;AAEF,QAAM,CAAC,CAAC,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI;AAKrE,MAAI,IAAI,IAAI;AACZ,MAAI,MAAM;AACP,KAAA,IAAI,IAAI,EAAE,IAAI;AAAA,EAAA,OACV;AACL,KAAC,IAAI,IAAI,EAAE,IAAI,wBAAwB,KAAK;AAAA,MAC1C,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEH,QAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AAC1C,QAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AAC1C,QAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AACnC,SAAA,CAAC,IAAI,IAAI,EAAE;AACpB;AASO,MAAM,2BAA2B,CACtC,QACA,QACA,OAAgB,UACS;AACzB,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAAA,WACvC,OAAO,WAAW,MAAM;AACjC,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,GAAG;AAAA,EAAA;AAE7D,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAAA,WACvC,OAAO,WAAW,MAAM;AACjC,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,GAAG;AAAA,EAAA;AAE7D,MAAI,IAAI;AACR,SAAO,IAAI,MAAM;AACf,QAAI,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,MAAM,MAAM;AAC5C,aAAO,CAAC,IAAI;AACZ,aAAO,CAAC,IAAI;AAAA,IACH,WAAA,OAAO,CAAC,MAAM,MAAM;AACtB,aAAA,CAAC,IAAI,OAAO,CAAC;AAAA,IACX,WAAA,OAAO,CAAC,MAAM,MAAM;AACtB,aAAA,CAAC,IAAI,OAAO,CAAC;AAAA,IAAA;AAEtB;AAAA,EAAA;AAEF,MAAI,CAAC,MAAM;AACT,aAAS,wBAAwB,QAAQ;AAAA,MACvC,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AACD,aAAS,wBAAwB,QAAQ;AAAA,MACvC,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEI,SAAA,CAAC,QAAQ,MAAM;AACxB;AAOa,MAAA,oBAAoB,CAAC,UAA0B;AAC1D,MAAI,CAAC,OAAO,SAAS,KAAK,GAAG;AAC3B,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA,OAC1C;AACG,YAAA,KAAK,MAAM,KAAK;AACpB,QAAA,QAAQ,KAAK,QAAQ,SAAS;AAChC,YAAM,IAAI,WAAW,GAAG,KAAK,yBAAyB,OAAO,GAAG;AAAA,IAAA;AAAA,EAClE;AAEE,MAAA,MAAM,MAAM,SAAS,GAAG;AACxB,MAAA,IAAI,WAAW,GAAG;AACpB,UAAM,IAAI,GAAG;AAAA,EAAA;AAER,SAAA;AACT;AAOa,MAAA,aAAa,CAAC,UAA0B;AAC/C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,OAAO,MAAM;AACb,QAAA,MAAM,OAAO,KAAK,KAAK;AAC7B,QAAM,MAAM,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,KAAK;AAC9C,MAAI,CAAC,IAAI,KAAK,KAAK,GAAG;AACpB,UAAM,IAAI,YAAY,2BAA2B,KAAK,EAAE;AAAA,EAAA;AAE1D,QAAM,CAAA,EAAG,KAAK,IAAI,IAAI,MAAM,MAAM,GAAG;AACrC,QAAM,QAAQ,IAAI,CAAC,MAAM,MAAM,IAAI,GAAG,KAAK;AACvC,MAAA;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACG,YAAA,WAAW,KAAK,IAAI;AAC1B;AAAA,IACF,KAAK;AACG,YAAA,WAAW,KAAK,IAAI;AAC1B;AAAA,IACF,KAAK;AACG,YAAA,WAAW,KAAK,IAAI;AAC1B;AAAA,IACF;AACE,YAAM,WAAW,KAAK;AAAA,EAAA;AAEnB,SAAA;AACP,MAAI,MAAM,GAAG;AACJ,WAAA;AAAA,EACE,WAAA,OAAO,GAAG,KAAK,EAAE,GAAG;AACvB,UAAA;AAAA,EAAA;AAED,SAAA;AACT;AAOa,MAAA,aAAa,CAAC,WAAmC;AAC5D,MAAI,QAA4C;AAC5C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACnB,QAAI,CAAC,OAAO;AACF,cAAA;AAAA,IAAA,WACC,UAAU,MAAM;AACjB,cAAA;AAAA,IAAA,OACH;AACD,UAAA,MAAM,CAAC,MAAM,KAAK;AACpB,gBAAQ,IAAI,KAAK;AAAA,MAAA;AAEf,UAAA,MAAM,SAAS,GAAG,GAAG;AACf,gBAAA,WAAW,KAAK,IAAI;AAAA,MAAA,OACvB;AACL,gBAAQ,WAAW,KAAK;AAAA,MAAA;AAE1B,UAAI,CAAC,OAAO,SAAS,KAAK,GAAG;AAC3B,cAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,MAAA;AAEjD,UAAI,QAAQ,MAAM;AACR,gBAAA;AAAA,MAAA,WACC,QAAQ,GAAG;AACZ,gBAAA;AAAA,MAAA,OACH;AACL,gBAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,MAAA;AAAA,IACrC;AAAA,EACF,OACK;AACG,YAAA;AAAA,EAAA;AAEH,SAAA;AACT;AAOa,MAAA,gBAAgB,CAAC,UAA0B;AAClD,MAAA,SAAS,KAAK,GAAG;AACnB,QAAI,UAAU,IAAI;AACV,YAAA,IAAI,YAAY,wCAAwC;AAAA,IAAA;AAEhE,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE7C,MAAA,QAAQ,SAAS,OAAO,GAAG;AAC/B,MAAI,SAAS,GAAG;AACP,WAAA;AAAA,EAAA;AAET,MAAI,SAAS,SAAS;AACb,WAAA;AAAA,EAAA;AAEH,QAAA,+BAAe,IAAI;AACzB,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,aAAS,IAAI,KAAK,MAAO,IAAI,UAAW,OAAO,GAAG,CAAC;AAAA,EAAA;AAEjD,MAAA,SAAS,IAAI,KAAK,GAAG;AACf,YAAA,SAAS,IAAI,KAAK,IAAI;AAAA,EAAA,OACzB;AACL,YAAQ,KAAK,MAAM,QAAQ,UAAU,IAAI,IAAI;AAAA,EAAA;AAE/C,SAAO,WAAW,MAAM,QAAQ,CAAC,CAAC;AACpC;AAQO,MAAM,wBAAwB,CACnC,KACA,OAAgB,UACE;AAClB,MAAI,IAAI,IAAI;AACZ,MAAI,MAAM;AACP,KAAA,IAAI,IAAI,EAAE,IAAI;AAAA,EAAA,OACV;AACL,KAAC,IAAI,IAAI,EAAE,IAAI,wBAAwB,KAAK;AAAA,MAC1C,WAAW;AAAA,MACX,UAAU;AAAA,IAAA,CACX;AAAA,EAAA;AAEH,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,QAAM,WAAW;AACjB,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,KAAK,IAAI,kBAAkB,IAAI,gBAAgB,UAAU;AAAA,EAAA,OAC7D;AACA,SAAA;AAAA,EAAA;AAEP,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,KAAK,IAAI,kBAAkB,IAAI,gBAAgB,UAAU;AAAA,EAAA,OAC7D;AACA,SAAA;AAAA,EAAA;AAEP,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,KAAK,IAAI,kBAAkB,IAAI,gBAAgB,UAAU;AAAA,EAAA,OAC7D;AACA,SAAA;AAAA,EAAA;AAEA,SAAA,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,MAAM,kBAAkB,CAC7B,KACA,OAAgB,UACE;AACd,MAAA,GAAG,GAAG,GAAG;AACb,MAAI,MAAM;AACR,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EAAA,OACd;AACL,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAAA,MAC9C,OAAO;AAAA,MACP,UAAU;AAAA,IAAA,CACX;AAAA,EAAA;AAEG,QAAA,CAAC,IAAI,IAAI,EAAE,IAAI,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC1D,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,IAAK,IAAK,EAAG,GAAG,IAAI;AAC5E,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAkBa,MAAA,kBAAkB,CAAC,QAA+B;AAC7D,QAAM,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAAA,IACpD,OAAO;AAAA,IACP,UAAU;AAAA,EAAA,CACX;AACK,QAAA,KAAK,kBAAkB,CAAC;AACxB,QAAA,KAAK,kBAAkB,CAAC;AACxB,QAAA,KAAK,kBAAkB,CAAC;AACxB,QAAA,KAAK,kBAAkB,QAAQ,OAAO;AACxC,MAAA;AACJ,MAAI,OAAO,MAAM;AACf,UAAM,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AAAA,EAAA,OACjB;AACL,UAAM,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAAA,EAAA;AAEtB,SAAA;AACT;AAQO,MAAM,wBAAwB,CACnC,KACA,QAAiB,UACC;AAClB,MAAI,CAAC,GAAG,GAAG,CAAC,IAAI,wBAAwB,KAAK;AAAA,IAC3C,WAAW;AAAA,EAAA,CACZ;AACD,QAAM,WAAW,MAAM;AACvB,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAAA,OACnD;AACA,SAAA;AAAA,EAAA;AAEF,OAAA;AACL,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAAA,OACnD;AACA,SAAA;AAAA,EAAA;AAEF,OAAA;AACL,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAAA,OACnD;AACA,SAAA;AAAA,EAAA;AAEF,OAAA;AACE,SAAA;AAAA,IACL,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,IACxB,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,IACxB,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,EAC1B;AACF;AAuFO,MAAM,kBAAkB,CAC7B,KACA,OAAgB,UACE;AACd,MAAA,GAAG,GAAG,GAAG;AACb,MAAI,MAAM;AACR,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EAAA,OACd;AACL,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAAA,MAC9C,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEH,MAAI,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AACnE,GAAA,GAAG,GAAG,CAAC,IAAI;AAAA,IACV;AAAA,MACE,KAAK,IAAI,KAAK,IAAI,GAAI,CAAC,GAAG,CAAC;AAAA,MAC3B,KAAK,IAAI,KAAK,IAAI,GAAI,CAAC,GAAG,CAAC;AAAA,MAC3B,KAAK,IAAI,KAAK,IAAI,GAAI,CAAC,GAAG,CAAC;AAAA,IAC7B;AAAA,IACA;AAAA,EACF;AACA,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAqBO,MAAM,kBAAkB,CAC7B,KACA,OAAgB,UACE;AACZ,QAAA,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,gBAAgB,KAAK,IAAI;AACrD,QAAM,IAAI,KAAM;AAChB,QAAM,IAAI,KAAM;AAChB,QAAM,IAAI,KAAM;AAChB,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,IAAI,MAAM;AACV,QAAA,KAAK,MAAM,OAAO,OAAO;AAC/B,MAAI,GAAG;AACH,MAAA,KAAK,MAAM,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,MAAM,SAAS;AAChD,QAAA;AACA,QAAA;AAAA,EAAA,OACC;AACL,QAAK,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,CAAC,KAAM;AAC1C,QAAI,MAAM,GAAG;AACP,UAAA;AAAA,IAAA,OACC;AACL,cAAQ,KAAK;AAAA,QACX,KAAK;AACH,eAAK,IAAI,KAAK;AACd;AAAA,QACF,KAAK;AACE,eAAA,IAAI,KAAK,IAAI;AAClB;AAAA,QACF,KAAK;AAAA,QACL;AACO,eAAA,IAAI,KAAK,IAAI;AAClB;AAAA,MAAA;AAEJ,UAAK,IAAI,OAAQ;AACjB,UAAI,IAAI,GAAG;AACJ,aAAA;AAAA,MAAA;AAAA,IACP;AAAA,EACF;AAEF,SAAO,CAAC,GAAG,GAAG,GAAG,KAAM;AACzB;AAQO,MAAM,kBAAkB,CAC7B,KACA,OAAgB,UACE;AACZ,QAAA,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK,IAAI;AAClD,QAAM,IAAI,KAAK,IAAI,GAAI,GAAI,CAAE,IAAI;AACjC,QAAM,KAAK,IAAI,KAAK,IAAI,GAAI,GAAI,CAAE,IAAI;AAClC,MAAA;AACA,MAAA,IAAI,OAAO,GAAG;AACZ,QAAA;AAAA,EAAA,OACC;AACJ,KAAA,CAAC,IAAI,gBAAgB,GAAG;AAAA,EAAA;AAE3B,SAAO,CAAC,GAAI,IAAI,SAAS,KAAK,SAAS,KAAM;AAC/C;AAQO,MAAM,oBAAoB,CAC/B,KACA,OAAgB,UACE;AACd,MAAA,GAAG,GAAG,GAAG;AACb,MAAI,MAAM;AACR,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EAAA,OACd;AACL,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAAA,MAC9C,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEG,QAAA,MAAM,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AACxD,QAAA,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC;AACtC,MAAA,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,QAAQ,IAAI;AACjE,MAAI,KAAK,IAAI,KAAK,IAAI,GAAI,CAAC,GAAG,CAAC;AACzB,QAAA,OAAO,KAAK,MAAM,WAAW,EAAE,QAAQ,IAAI,CAAC,IAAI,OAAO;AACzD,MAAA,SAAS,KAAK,SAAS,SAAS;AAC9B,QAAA;AACA,QAAA;AAAA,EAAA;AAEN,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,MAAM,oBAAoB,CAC/B,KACA,OAAgB,UACE;AACZ,QAAA,CAAC,GAAG,GAAG,GAAG,EAAE,IAAI,kBAAkB,KAAK,IAAI;AACjD,MAAI,GAAG;AACD,QAAA,OAAO,KAAK,MAAM,WAAW,EAAG,QAAQ,IAAI,CAAC,IAAI,OAAO;AAC1D,MAAA,SAAS,KAAK,SAAS,SAAS;AAC9B,QAAA;AACA,QAAA;AAAA,EAAA,OACC;AACL,QAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GAAI,OAAO,IAAI,KAAK,IAAI,GAAI,OAAO,CAAC,GAAG,CAAC;AACxE,QAAI,WAAW,EAAE,QAAQ,IAAI,CAAC,MAAM,GAAG;AACjC,UAAA;AAAA,IAAA,OACC;AACL,UAAK,KAAK,MAAM,GAAI,CAAE,IAAI,MAAM,OAAQ,KAAK;AAC7C,UAAI,IAAI,GAAG;AACJ,aAAA;AAAA,MAAA;AAAA,IACP;AAAA,EACF;AAEF,SAAO,CAAC,GAAI,GAAG,GAAG,EAAG;AACvB;AAQO,MAAM,qBAAqB,CAChC,KACA,OAAgB,UACE;AACd,MAAA,GAAG,GAAG,GAAG;AACb,MAAI,MAAM;AACR,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EAAA,OACd;AACL,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAAA,MAC9C,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEG,QAAA,SAAS,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AACjE,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,QAAQ,IAAI;AAC9C,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,MAAM,qBAAqB,CAChC,KACA,OAAgB,UACE;AACd,MAAA,GAAG,GAAG,GAAG;AACb,MAAI,MAAM;AACR,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EAAA,OACd;AACL,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAAA,MAC9C,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEH,QAAM,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,MAAM,MAAM,IAAI,CAAC,CAAE;AACtD,QAAM,CAAC,IAAI,IAAI,EAAE,IAAI,OAAO;AAAA,IAAI,CAAC,QAC/B,MAAM,cAAc,KAAK,KAAK,GAAG,KAAK,MAAM,YAAY,OAAO;AAAA,EACjE;AACM,QAAA,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAM,KAAK,CAAC,GAAG,OAAO;AAC1D,MAAI,GAAG;AACH,MAAA,MAAM,KAAK,MAAM,SAAS;AACxB,QAAA;AACA,QAAA;AAAA,EAAA,OACC;AACL,SAAK,KAAM,MAAO;AAClB,SAAK,KAAM,MAAO;AAAA,EAAA;AAEpB,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,MAAM,qBAAqB,CAChC,KACA,OAAgB,UACE;AACZ,QAAA,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,mBAAmB,KAAK,IAAI;AACrD,MAAI,GAAG;AACH,MAAA,MAAM,KAAK,MAAM,SAAS;AACxB,QAAA;AACA,QAAA;AAAA,EAAA,OACC;AACL,QAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GAAI,OAAO,IAAI,KAAK,IAAI,GAAI,OAAO,CAAC,GAAG,CAAC;AACxE,QAAK,KAAK,MAAM,GAAI,CAAE,IAAI,MAAM,OAAQ,KAAK;AAC7C,QAAI,IAAI,GAAG;AACJ,WAAA;AAAA,IAAA;AAAA,EACP;AAEF,SAAO,CAAC,GAAI,GAAG,GAAG,KAAM;AAC1B;AAOa,MAAA,kBAAkB,CAAC,UAAiC;AAC3D,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,MACE,EACE,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK,IAE5B;AACA,UAAM,IAAI,YAAY,2BAA2B,KAAK,EAAE;AAAA,EAAA;AAE1D,QAAM,MAAM,CAAC;AACT,MAAA,gBAAgB,KAAK,KAAK,GAAG;AAC/B,UAAM,CAAG,EAAA,GAAG,GAAG,CAAC,IAAI,MAAM;AAAA,MACxB;AAAA,IACF;AACA,QAAI,KAAK,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EACvD,WAAA,gBAAgB,KAAK,KAAK,GAAG;AAChC,UAAA,CAAA,EAAG,GAAG,GAAG,CAAC,IAAI,MAAM,MAAM,gCAAgC;AAM5D,QAAA;AAAA,MACF,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB;AAAA,IACF;AAAA,EACS,WAAA,gBAAgB,KAAK,KAAK,GAAG;AACtC,UAAM,CAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI,MAAM;AAAA,MAC/B;AAAA,IACF;AACI,QAAA;AAAA,MACF,SAAS,GAAG,GAAG;AAAA,MACf,SAAS,GAAG,GAAG;AAAA,MACf,SAAS,GAAG,GAAG;AAAA,MACf,cAAc,KAAK;AAAA,IACrB;AAAA,EACS,WAAA,gBAAgB,KAAK,KAAK,GAAG;AACtC,UAAM,CAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI,MAAM;AAAA,MAC/B;AAAA,IACF;AACI,QAAA;AAAA,MACF,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,cAAc,GAAG,KAAK,GAAG,KAAK,EAAE;AAAA,IAClC;AAAA,EAAA;AAEK,SAAA;AACT;AAOa,MAAA,wBAAwB,CAAC,UAAiC;AACrE,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,gBAAgB,KAAK;AAC3C,QAAA,CAAC,GAAG,GAAG,CAAC,IAAI,sBAAsB,CAAC,IAAK,IAAK,EAAG,GAAG,IAAI;AAC7D,SAAO,CAAC,GAAI,GAAI,GAAI,KAAM;AAC5B;AAOa,MAAA,kBAAkB,CAAC,UAAiC;AAC/D,QAAM,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,sBAAsB,KAAK;AACpD,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAI,GAAI,CAAE,GAAG,IAAI;AACzE,SAAO,CAAC,GAAI,GAAI,GAAI,KAAM;AAC5B;AASO,MAAM,WAAW,CACtB,OACA,MAEI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,QAAM,MAAM,IAAI,OAAO,iBAAiB,OAAO,IAAI,WAAW,WAAW;AACzE,MAAI,CAAC,IAAI,KAAK,KAAK,GAAG;AACpB,YAAQ,QAAQ;AAAA,MACd,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEF,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,GAAG;AAE/B,MAAI,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,QAAQ,SAAS,GAAG,EAAE,MAAM,KAAK;AAM5D,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,CAAC,MAAM,KAAK;AACjB,WAAK,IAAI,EAAE;AAAA,IAAA;AAET,QAAA,GAAG,SAAS,GAAG,GAAG;AACf,UAAA,WAAW,EAAE,IAAI,UAAW;AAAA,IAAA,OAC5B;AACL,UAAI,WAAW,EAAE;AAAA,IAAA;AAEf,QAAA,KAAK,IAAI,KAAK,IAAI,iBAAiB,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAE7D,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,CAAC,MAAM,KAAK;AACjB,WAAK,IAAI,EAAE;AAAA,IAAA;AAET,QAAA,GAAG,SAAS,GAAG,GAAG;AACf,UAAA,WAAW,EAAE,IAAI,UAAW;AAAA,IAAA,OAC5B;AACL,UAAI,WAAW,EAAE;AAAA,IAAA;AAEf,QAAA,KAAK,IAAI,KAAK,IAAI,iBAAiB,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAE7D,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,CAAC,MAAM,KAAK;AACjB,WAAK,IAAI,EAAE;AAAA,IAAA;AAET,QAAA,GAAG,SAAS,GAAG,GAAG;AACf,UAAA,WAAW,EAAE,IAAI,UAAW;AAAA,IAAA,OAC5B;AACL,UAAI,WAAW,EAAE;AAAA,IAAA;AAEf,QAAA,KAAK,IAAI,KAAK,IAAI,iBAAiB,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAEvD,QAAA,QAAQ,WAAW,EAAE;AACpB,SAAA,CAAC,OAAO,GAAG,GAAG,GAAG,WAAW,WAAW,OAAO,OAAO,OAAO,KAAK;AAC1E;AAUO,MAAM,WAAW,CACtB,OACA,MAEI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEF,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,MAAI,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,QAAQ,SAAS,GAAG,EAAE,MAAM,KAAK;AAM5D,MAAI,MAAM,MAAM;AACd,QAAI,WAAW,OAAO;AAChB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACL,QAAI,WAAW,CAAW;AAAA,EAAA;AAE5B,MAAI,MAAM,MAAM;AACd,QAAI,WAAW,OAAO;AAChB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEP,QAAA,KAAK,IAAI,KAAK,IAAI,WAAW,CAAW,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAE5D,MAAI,MAAM,MAAM;AACd,QAAI,WAAW,OAAO;AAChB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEP,QAAA,KAAK,IAAI,KAAK,IAAI,WAAW,CAAW,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAExD,MAAA,UAAU,QAAQ,WAAW,OAAO;AACtC,YAAQ,WAAW,KAAe;AAAA,EAAA;AAEpC,MAAI,WAAW,OAAO;AACpB,WAAO,CAAC,QAAQ,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAEhC,QAAM,KAAM,IAAe;AAC3B,QAAM,KAAO,IAAe,UAAW,KAAK,IAAI,IAAI,IAAI,EAAE;AACpD,QAAA,KAAQ,IAAe,MAAO,MAAO;AAC3C,QAAM,MAAM,IAAM,IAAe,MAAO,OAAO;AAC/C,QAAM,MAAM,IAAM,IAAe,MAAO,OAAO;AAC/C,QAAM,IACJ,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC;AACrE,QAAM,IACJ,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC;AACrE,QAAM,IACJ,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC;AAC9D,SAAA;AAAA,IACL;AAAA,IACA,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,IACjE,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,IACjE,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,IACjE;AAAA,EACF;AACF;AAUO,MAAM,WAAW,CACtB,OACA,MAEI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,YAAQ,QAAQ;AAAA,MACd,KAAK;AAAA,MACL,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEF,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,MAAI,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE,MAAM,KAAK;AAMxD,MAAI,MAAM,MAAM;AACd,QAAI,WAAW,OAAO;AAChB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACL,QAAI,WAAW,CAAW;AAAA,EAAA;AAE5B,MAAI,MAAM,MAAM;AACd,QAAI,WAAW,OAAO;AAChB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEP,QAAA,KAAK,IAAI,KAAK,IAAI,WAAW,CAAW,GAAG,CAAC,GAAG,OAAO,IAAI;AAAA,EAAA;AAEhE,MAAI,MAAM,MAAM;AACd,QAAI,WAAW,OAAO;AAChB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEP,QAAA,KAAK,IAAI,KAAK,IAAI,WAAW,CAAW,GAAG,CAAC,GAAG,OAAO,IAAI;AAAA,EAAA;AAE5D,MAAA,UAAU,QAAQ,WAAW,OAAO;AACtC,YAAQ,WAAW,KAAe;AAAA,EAAA;AAEpC,MAAI,WAAW,OAAO;AACb,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA,MAAM,OAAO,IAAI,IAAI;AAAA,MACrB,MAAM,OAAO,IAAI,IAAI;AAAA,MACrB;AAAA,IACF;AAAA,EAAA;AAEG,MAAA,IAAgB,KAAgB,GAAG;AACtC,UAAM,IAAI;AAAA,MACN,KAAiB,IAAgB,KAAiB;AAAA,MACpD;AAAA,IACF;AACA,WAAO,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAEzB,QAAA,UAAU,IAAK,IAAgB,KAAgB;AACjD,MAAA,CAAG,EAAA,IAAI,IAAI,EAAE,IAAI,SAAS,OAAO,CAAC,UAAU;AAM3C,OAAA;AAAA,KACD,KAAgB,SAAU,KAAgB;AAAA,IAC5C;AAAA,EACF;AACK,OAAA;AAAA,KACD,KAAgB,SAAU,KAAgB;AAAA,IAC5C;AAAA,EACF;AACK,OAAA;AAAA,KACD,KAAgB,SAAU,KAAgB;AAAA,IAC5C;AAAA,EACF;AACO,SAAA;AAAA,IACL;AAAA,IACA,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,OAAO;AAAA,IACjC,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,OAAO;AAAA,IACjC,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,GAAG,OAAO;AAAA,IACjC;AAAA,EACF;AACF;AAUO,MAAM,WAAW,CACtB,OACA,MAEI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,YAAQ,QAAQ;AAAA,MACd,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEF,QAAM,WAAW;AACjB,QAAM,WAAW;AACjB,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,MAAI,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE,MAAM,KAAK;AAMxD,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEN,QAAA,EAAa,SAAS,GAAG,GAAG;AAC/B,UAAI,WAAW,CAAW;AAC1B,UAAI,IAAI,SAAS;AACX,YAAA;AAAA,MAAA;AAAA,IACN,OACK;AACL,UAAI,WAAW,CAAW;AAAA,IAAA;AAE5B,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEN,QAAA,EAAa,SAAS,GAAG,GAAG;AAC3B,UAAA,WAAW,CAAW,IAAI;AAAA,IAAA,OACzB;AACL,UAAI,WAAW,CAAW;AAAA,IAAA;AAAA,EAC5B;AAEF,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,SAAS,GAAG,GAAG;AAC3B,UAAA,WAAW,CAAW,IAAI;AAAA,IAAA,OACzB;AACL,UAAI,WAAW,CAAW;AAAA,IAAA;AAAA,EAC5B;AAEF,MAAI,UAAU,QAAQ,CAAC,SAAS,KAAK,MAAO,GAAG;AAC7C,YAAQ,WAAW,KAAe;AAAA,EAAA;AAEhC,MAAA,SAAS,KAAK,MAAO,GAAG;AACnB,WAAA;AAAA,MACL;AAAA,MACA,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC;AAAA,IACF;AAAA,EAAA;AAEI,QAAA,MAAO,IAAe,OAAO;AAC7B,QAAA,KAAM,IAAe,QAAQ;AAC7B,QAAA,KAAK,KAAM,IAAe;AAChC,QAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ;AACnC,QAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ;AACnC,QAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ;AACnC,QAAM,MAAM;AAAA,IACV,QAAQ,cAAc,SAAS,KAAK,QAAQ,OAAO;AAAA,IAClD,IAAe,WAAW,QAAS,IAAe;AAAA,IACnD,QAAQ,cAAc,SAAS,KAAK,QAAQ,OAAO;AAAA,EACrD;AACA,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,CAACA,MAAK,MAAMA,OAAM,IAAI,CAAC,CAAE;AAC5C,SAAA;AAAA,IACL;AAAA,IACA,iBAAiB,GAAI,GAAG;AAAA,IACxB,iBAAiB,GAAI,GAAG;AAAA,IACxB,iBAAiB,GAAI,GAAG;AAAA,IACxB;AAAA,EACF;AACF;AAUO,MAAM,WAAW,CACtB,OACA,MAEI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,YAAQ,QAAQ;AAAA,MACd,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEF,QAAM,WAAW;AACjB,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,MAAI,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE,MAAM,KAAK;AAMxD,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEX,QAAI,WAAW,CAAW;AAC1B,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEN,QAAA,EAAa,SAAS,GAAG,GAAG;AAC3B,UAAA,WAAW,CAAW,IAAI;AAAA,IAAA,OACzB;AACL,UAAI,WAAW,CAAW;AAAA,IAAA;AAAA,EAC5B;AAEF,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACL,QAAI,WAAW,CAAW;AAAA,EAAA;AAE5B,MAAI,UAAU,QAAQ,CAAC,SAAS,KAAK,MAAO,GAAG;AAC7C,YAAQ,WAAW,KAAe;AAAA,EAAA;AAEhC,MAAA,SAAS,KAAK,MAAO,GAAG;AACnB,WAAA;AAAA,MACL;AAAA,MACA,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC;AAAA,IACF;AAAA,EAAA;AAEI,QAAA,IAAK,IAAe,KAAK,IAAM,IAAe,KAAK,MAAO,MAAM,KAAK;AACrE,QAAA,IAAK,IAAe,KAAK,IAAM,IAAe,KAAK,MAAO,MAAM,KAAK;AAC3E,QAAM,CAAG,EAAA,GAAG,GAAG,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AAM3C,SAAA;AAAA,IACL;AAAA,IACA,iBAAiB,GAAa,GAAG;AAAA,IACjC,iBAAiB,GAAa,GAAG;AAAA,IACjC,iBAAiB,GAAa,GAAG;AAAA,IACjC;AAAA,EACF;AACF;AAUO,MAAM,aAAa,CACxB,OACA,MAEI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,YAAQ,QAAQ;AAAA,MACd,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEF,QAAM,WAAW;AACjB,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,SAAS;AACrC,MAAI,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE,MAAM,KAAK;AAMxD,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEN,QAAA,EAAa,SAAS,GAAG,GAAG;AAC3B,UAAA,WAAW,CAAW,IAAI;AAAA,IAAA,OACzB;AACL,UAAI,WAAW,CAAW;AAAA,IAAA;AAE5B,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEN,QAAA,EAAa,SAAS,GAAG,GAAG;AAC1B,UAAA,WAAW,CAAW,IAAI,WAAY;AAAA,IAAA,OACtC;AACL,UAAI,WAAW,CAAW;AAAA,IAAA;AAAA,EAC5B;AAEF,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,SAAS,GAAG,GAAG;AAC1B,UAAA,WAAW,CAAW,IAAI,WAAY;AAAA,IAAA,OACtC;AACL,UAAI,WAAW,CAAW;AAAA,IAAA;AAAA,EAC5B;AAEF,MAAI,UAAU,QAAQ,CAAC,SAAS,KAAK,MAAO,GAAG;AAC7C,YAAQ,WAAW,KAAe;AAAA,EAAA;AAEhC,MAAA,SAAS,KAAK,MAAO,GAAG;AACnB,WAAA;AAAA,MACL;AAAA,MACA,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC;AAAA,IACF;AAAA,EAAA;AAEI,QAAA,MAAM,gBAAgB,qBAAqB;AAAA,IAC/C;AAAA,IACA;AAAA,IACA;AAAA,EAAA,CACD;AACK,QAAA,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,GAAG,QAAQ,CAAC;AAC7C,QAAA,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,QAAQ,IAAI;AAC1D,SAAA;AAAA,IACL;AAAA,IACA,iBAAiB,GAAI,GAAG;AAAA,IACxB,iBAAiB,GAAI,GAAG;AAAA,IACxB,iBAAiB,GAAI,GAAG;AAAA,IACxB;AAAA,EACF;AACF;AAUO,MAAM,aAAa,CACxB,OACA,MAEI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,YAAQ,QAAQ;AAAA,MACd,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEF,QAAM,WAAW;AACjB,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,SAAS;AACrC,MAAI,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE,MAAM,KAAK;AAMxD,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEN,QAAA,EAAa,SAAS,GAAG,GAAG;AAC3B,UAAA,WAAW,CAAW,IAAI;AAAA,IAAA,OACzB;AACL,UAAI,WAAW,CAAW;AAAA,IAAA;AAE5B,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACA,QAAA,EAAa,CAAC,MAAM,KAAK;AAC5B,UAAI,IAAI,CAAC;AAAA,IAAA;AAEN,QAAA,EAAa,SAAS,GAAG,GAAG;AAC1B,UAAA,WAAW,CAAW,IAAI,WAAY;AAAA,IAAA,OACtC;AACL,UAAI,WAAW,CAAW;AAAA,IAAA;AAE5B,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,MAAM,MAAM;AACd,QAAI,CAAC,SAAS,KAAK,MAAO,GAAG;AACvB,UAAA;AAAA,IAAA;AAAA,EACN,OACK;AACL,QAAI,WAAW,CAAW;AAAA,EAAA;AAE5B,MAAI,UAAU,QAAQ,CAAC,SAAS,KAAK,MAAO,GAAG;AAC7C,YAAQ,WAAW,KAAe;AAAA,EAAA;AAEhC,MAAA,SAAS,KAAK,MAAO,GAAG;AACnB,WAAA;AAAA,MACL;AAAA,MACA,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC,MAAM,OAAO,IAAI,iBAAiB,GAAG,GAAG;AAAA,MACxC;AAAA,IACF;AAAA,EAAA;AAEI,QAAA,IAAK,IAAe,KAAK,IAAM,IAAe,KAAK,MAAO,MAAM,KAAK;AACrE,QAAA,IAAK,IAAe,KAAK,IAAM,IAAe,KAAK,MAAO,MAAM,KAAK;AAC3E,QAAM,MAAM,gBAAgB,qBAAqB,CAAC,GAAa,GAAG,CAAC,CAAC;AAC9D,QAAA,SAAS,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,QAAQ,CAAC;AAC/C,QAAA,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,QAAQ,IAAI;AAC1D,SAAA;AAAA,IACL;AAAA,IACA,iBAAiB,GAAI,GAAG;AAAA,IACxB,iBAAiB,GAAI,GAAG;AAAA,IACxB,iBAAiB,GAAI,GAAG;AAAA,IACxB;AAAA,EACF;AACF;AAYO,MAAM,iBAAiB,CAC5B,OACA,MAII,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,YAAY,KAAK,OAAW,IAAA;AACpC,MAAI,CAAC,aAAa,KAAK,KAAK,GAAG;AAC7B,YAAQ,QAAQ;AAAA,MACd,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEF,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,YAAY;AACxC,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE,MAAM,KAAK;AAO5D,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,OAAO;AACX,SAAA;AAAA,EAAA;AAEP,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,CAAC,MAAM,KAAK;AACjB,WAAK,IAAI,EAAE;AAAA,IAAA;AAET,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAAA,EAAA;AAEjE,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,CAAC,MAAM,KAAK;AACjB,WAAK,IAAI,EAAE;AAAA,IAAA;AAET,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAAA,EAAA;AAEjE,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,CAAC,MAAM,KAAK;AACjB,WAAK,IAAI,EAAE;AAAA,IAAA;AAET,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAAA,EAAA;AAE3D,QAAA,QAAQ,WAAW,EAAE;AAC3B,MAAI,SAAS,KAAK,MAAO,KAAM,WAAW,WAAW,OAAO,YAAa;AAChE,WAAA;AAAA,MACL;AAAA,MACA,OAAO,OAAO,OAAO,iBAAiB,GAAG,GAAG;AAAA,MAC5C,OAAO,OAAO,OAAO,iBAAiB,GAAG,GAAG;AAAA,MAC5C,OAAO,OAAO,OAAO,iBAAiB,GAAG,GAAG;AAAA,MAC5C,OAAO,OAAO,OAAO;AAAA,IACvB;AAAA,EAAA;AAEF,MAAI,GAAG,GAAG;AAEV,MAAI,OAAO,QAAQ;AACjB,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,CAAC,IAAI,SAAS,IAAI,SAAS,IAAI,OAAO,CAAC;AACnE,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EACF,WAES,OAAO,eAAe;AAC9B,KAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1D,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EACF,WAES,OAAO,cAAc;AAC9B,UAAM,YAAY,sBAAsB;AAAA,MACtC,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IAAA,CACL;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,kBAAkB,SAAS;AACvD,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EACF,WAES,OAAO,WAAW;AAC3B,UAAM,QAAQ;AACd,UAAM,OAAO;AACb,UAAM,WAAW;AACX,UAAA,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM;AAC3B,UAAA;AACA,UAAA,IAAI,OAAO,WAAW,KAAK;AAC7B,aAAK,KAAK,WAAW;AAAA,MAAA,OAChB;AACL,aAAK,KAAK,KAAK,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ;AAAA,MAAA;AAE9C,aAAA;AAAA,IAAA,CACR;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,uBAAuB,GAAG;AACtD,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EACF,WAES,OAAO,WAAW;AAC3B,UAAM,UAAU,MAAM;AAChB,UAAA,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM;AAC/B,YAAM,KAAK,KAAK,IAAI,GAAG,OAAO;AACvB,aAAA;AAAA,IAAA,CACR;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,GAAG;AAClD,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EACF,WAES,OAAO,gBAAgB;AAChC,UAAM,eAAe;AACf,UAAA,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM;AAC3B,UAAA;AACA,UAAA,IAAI,KAAK,MAAM,MAAM;AAClB,aAAA,KAAK,IAAI,GAAG,YAAY;AAAA,MAAA,OACxB;AACL,aAAK,IAAI;AAAA,MAAA;AAEJ,aAAA;AAAA,IAAA,CACR;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,4BAA4B,GAAG;AAC3D,QAAI,CAAC,KAAK;AACP,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EAGO,WAAA,wBAAwB,KAAK,EAAE,GAAG;AAC3C,KAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AACpB,QAAI,OAAO,WAAW;AACpB,UAAI,CAAC,KAAK;AACP,SAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MAAA;AAAA,eAEjD,KAAK;AACb,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAChE;AAEK,SAAA;AAAA,IACL,MAAM,YAAY;AAAA,IAClB,iBAAiB,GAAI,GAAG;AAAA,IACxB,iBAAiB,GAAI,GAAG;AAAA,IACxB,iBAAiB,GAAI,GAAG;AAAA,IACxB,WAAW,WAAW,OAAO,OAAO,OAAO;AAAA,EAC7C;AACF;AAYO,MAAM,kBAAkB,CAC7B,OACA,MAGI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,KAAK,OAAA,IAAW;AAExB,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,YAAQ,QAAQ;AAAA,MACd,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEE,MAAA,GAAG,GAAG,GAAG;AAET,MAAA,YAAY,KAAK,KAAK,GAAG;AAC3B,QAAI,WAAW,UAAU;AACvB,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAE3B,QAAI,WAAW,UAAU;AAChB,aAAA;AAAA,IAAA;AAEL,QAAA;AACA,QAAA;AACA,QAAA;AACI,YAAA;AAAA,EAEC,WAAA,WAAW,KAAK,KAAK,GAAG;AACjC,QAAI,OAAO,UAAU,eAAe,KAAK,cAAc,KAAK,GAAG;AAC7D,UAAI,WAAW,UAAU;AAChB,eAAA;AAAA,MAAA;AAET,YAAM,CAAC,GAAG,GAAG,CAAC,IAAI,aAAa,KAAkC;AAKzD,cAAA;AACR,UAAI,WAAW,UAAU;AACvB,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;AAAA,MAAA;AAE9B,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC3C,UAAI,KAAK;AACN,SAAA,GAAG,GAAG,CAAC,IAAI;AAAA,UACV;AAAA,UACA,CAAC,GAAa,GAAa,CAAW;AAAA,UACtC;AAAA,QACF;AAAA,MAAA;AAAA,IACF,OACK;AACL,UAAI,WAAW,UAAU;AACvB,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAE3B,UAAI,WAAW,UAAU;AACvB,YAAI,UAAU,eAAe;AACpB,iBAAA;AAAA,QAAA;AAEF,eAAA;AAAA,MAAA;AAET,UAAI,WAAW,SAAS;AACtB,YAAI,UAAU,eAAe;AAC3B,iBAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,QAAA;AAEpB,eAAA;AAAA,MAAA;AAEL,UAAA;AACA,UAAA;AACA,UAAA;AACI,cAAA;AAAA,IAAA;AAAA,EAGD,WAAA,MAAM,CAAC,MAAM,KAAK;AACvB,QAAA,SAAS,KAAK,MAAO,GAAG;AACpB,YAAA,MAAM,gBAAgB,KAAK;AAC1B,aAAA,CAAC,OAAO,GAAG,GAAG;AAAA,IAAA;AAEvB,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AACxC,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EAGO,WAAA,MAAM,WAAW,KAAK,GAAG;AAC9B,QAAA,SAAS,KAAK,MAAO,GAAG;AACnB,aAAA,SAAS,OAAO,GAAG;AAAA,IAAA;AAE5B,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AAOnC,QAAI,CAAC,KAAK;AACP,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EAGO,WAAA,MAAM,WAAW,KAAK,GAAG;AAC9B,QAAA,SAAS,KAAK,MAAO,GAAG;AACnB,aAAA,SAAS,OAAO,GAAG;AAAA,IAAA;AAE5B,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AAOnC,QAAI,CAAC,KAAK;AACP,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EAGO,WAAA,MAAM,WAAW,OAAO,GAAG;AAChC,QAAA,SAAS,KAAK,MAAO,GAAG;AACnB,aAAA,WAAW,OAAO,GAAG;AAAA,IAAA;AAE9B,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,KAAK;AAOrC,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EAGO,WAAA,MAAM,WAAW,OAAO,GAAG;AAChC,QAAA,SAAS,KAAK,MAAO,GAAG;AACnB,aAAA,WAAW,OAAO,GAAG;AAAA,IAAA;AAE9B,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,KAAK;AAOrC,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EACF,OACK;AACL,QAAI,GAAG,GAAG;AAEN,QAAA,MAAM,WAAW,KAAK,GAAG;AAC3B,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AAAA,IAQ1B,WAAA,MAAM,WAAW,KAAK,GAAG;AAClC,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AAAA,IAAA,OAQ9B;AACJ,OAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,IAAA;AAQtC,QAAA,SAAS,KAAK,MAAO,GAAG;AACnB,aAAA;AAAA,QACL;AAAA,QACA,KAAK,MAAM,CAAW;AAAA,QACtB,KAAK,MAAM,CAAW;AAAA,QACtB,KAAK,MAAM,CAAW;AAAA,QACtB;AAAA,MACF;AAAA,IAAA;AAED,KAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,CAAC,GAAa,GAAa,CAAW,CAAC;AACnE,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI;AAAA,QACV;AAAA,QACA,CAAC,GAAa,GAAa,CAAW;AAAA,QACtC;AAAA,MACF;AAAA,IAAA;AAAA,EACF;AAEK,SAAA;AAAA,IACL,MAAM,YAAY;AAAA,IAClB,iBAAiB,GAAa,GAAG;AAAA,IACjC,iBAAiB,GAAa,GAAG;AAAA,IACjC,iBAAiB,GAAa,GAAG;AAAA,IACjC;AAAA,EACF;AACF;AAWO,MAAM,oBAAoB,CAC/B,OACA,MAGI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,YAAY,OAAA,IAAW;AAE/B,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,YAAQ,QAAQ;AAAA,MACd,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEE,MAAA,IAAI,GAAG,GAAG,GAAG;AAEb,MAAA,YAAY,KAAK,KAAK,GAAG;AAC3B,QAAI,WAAW,UAAU;AAChB,aAAA;AAAA,IAAA;AAEL,QAAA;AACA,QAAA;AACA,QAAA;AACI,YAAA;AAAA,EAEC,WAAA,WAAW,KAAK,KAAK,GAAG;AACjC,QAAI,OAAO,UAAU,eAAe,KAAK,cAAc,KAAK,GAAG;AAC7D,UAAI,WAAW,UAAU;AAChB,eAAA;AAAA,MAAA;AAET,OAAC,GAAG,GAAG,CAAC,IAAI,aAAa,KAAkC;AAKnD,cAAA;AAAA,IAAA,OACH;AACL,UAAI,WAAW,UAAU;AACvB,YAAI,UAAU,eAAe;AACpB,iBAAA;AAAA,QAAA;AAEF,eAAA;AAAA,MAAA;AAET,UAAI,WAAW,SAAS;AACtB,YAAI,UAAU,eAAe;AAC3B,iBAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,QAAA;AAEpB,eAAA;AAAA,MAAA;AAEL,UAAA;AACA,UAAA;AACA,UAAA;AACI,cAAA;AAAA,IAAA;AAAA,EAGD,WAAA,MAAM,CAAC,MAAM,KAAK;AAC3B,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAO/B,WAAA,MAAM,WAAW,KAAK,GAAG;AACjC,KAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,EAQ/B,WAAA,MAAM,WAAW,KAAK,GAAG;AACjC,KAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,EAQ/B,WAAA,MAAM,WAAW,KAAK,GAAG;AACjC,KAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,EAQ/B,WAAA,cAAc,KAAK,KAAK,GAAG;AACpC,QAAI,GAAG,GAAG;AACN,QAAA,MAAM,WAAW,KAAK,GAAG;AAC1B,OAAA,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,IAAA,OAOrC;AACJ,OAAA,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,IAAA;AAQxC,QAAA,SAAS,KAAK,MAAO,GAAG;AAC1B,aAAO,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK;AAAA,IAAA;AAE5B,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,mBAAmB;AAAA,MACpC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;AAAA,EAEQ,WAAA,gBAAgB,KAAK,KAAK,GAAG;AACtC,QAAI,GAAG,GAAG;AACN,QAAA,MAAM,WAAW,OAAO,GAAG;AAC5B,OAAA,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO,GAAG;AAAA,IAAA,OAOvC;AACJ,OAAA,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO,GAAG;AAAA,IAAA;AAQ1C,QAAA,SAAS,KAAK,MAAO,GAAG;AAC1B,aAAO,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK;AAAA,IAAA;AAE5B,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA,CACD;AAAA,EAAA;AAEC,MAAA,WAAW,WAAW,eAAe,QAAQ;AACxC,WAAA;AAAA,MACL;AAAA,MACC,IAAe;AAAA,MACf,IAAe;AAAA,MACf,IAAe;AAAA,MAChB;AAAA,IACF;AAAA,EAAA;AAEK,SAAA;AAAA,IACL;AAAA,IACA,KAAK,MAAM,CAAW;AAAA,IACtB,KAAK,MAAM,CAAW;AAAA,IACtB,KAAK,MAAM,CAAW;AAAA,IACtB;AAAA,EACF;AACF;AAWO,MAAM,mBAAmB,CAC9B,OACA,MAGI,OACuC;AACvC,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,YAAY,OAAA,IAAW;AAC/B,MAAI,CAAC,aAAa,KAAK,KAAK,GAAG;AAC7B,YAAQ,QAAQ;AAAA,MACd,KAAK,SAAS;AACL,eAAA;AAAA,MAAA;AAAA,MAET,KAAK,UAAU;AACN,eAAA;AAAA,MAAA;AAAA,MAET,SAAS;AACP,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B;AAAA,EACF;AAEI,QAAA,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,OAAO,GAAG;AAOtD,MAAI,SAAS,KAAK,MAAO,KACpB,WAAW,WAAW,OAAO,YAAa;AAC7C,WAAO,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAE5B,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,KACC,IAAI;AACP,SAAO,CAAC,OAAO,GAAG,GAAG,GAAG,KAAe;AACzC;AAUO,MAAM,0BAA0B,CACrC,OACA,MAGI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,YAAY,OAAA,IAAW;AAC/B,MAAI,IAAI,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;AAC9B,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,QAAQ,MAAM;AACT,aAAA;AAAA,IAAA;AAET,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAOvB,QAAI,OAAO,YAAY;AACrB,aAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,IAAA;AAEvB,KAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,EAKvD,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,YAAY;AAClC,UAAA,CAACC,GAAE,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE,MAAM,KAAK;AAC9C,QAAIA,QAAO,eAAe;AACxB,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,QAC3C,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA,OACI;AACL,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAOxC,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAKlE,OACK;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAOzC,KAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,EAAA;AAM3D,SAAA;AAAA,IACL,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC1B;AAAA,EACF;AACF;AASO,MAAM,oBAAoB,CAC/B,OACA,MAGI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACf,MAAA,GAAG,GAAG,GAAG;AACb,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,iBAAiB,OAAO,GAAG;AAAA,IAAA,OAC5B;AACC,YAAA,kBAAkB,OAAO,GAAG;AAAA,IAAA;AAEpC,QAAI,QAAQ,MAAM;AACT,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,YAAY;AAClC,UAAA,CAAC,EAAE,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE,MAAM,KAAK;AAC9C,QAAI,OAAO,QAAQ;AACjB,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,QAC3C,QAAQ;AAAA,MAAA,CACT;AACI,WAAA;AACA,WAAA;AACA,WAAA;AAAA,IAAA,OACA;AACL,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,KAAK;AAAA,IAAA;AAAA,EAQpC,WAAA,qBAAqB,KAAK,KAAK,GAAG;AAC3C,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAM/C,KAAA,GAAG,GAAG,CAAC,IAAI,sBAAsB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAAA,OACtC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,kBAAkB,OAAO;AAAA,MAC5C,QAAQ;AAAA,IAAA,CACT;AAAA,EAAA;AAEH,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAUO,MAAM,oBAAoB,CAC/B,OACA,MAII,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,KAAK,OAAA,IAAW;AACpB,MAAA,GAAG,GAAG,GAAG;AACb,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,QAAQ,MAAM;AACT,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,YAAY;AAClC,UAAA,CAAC,EAAE,IAAI,IAAI,QAAQ,KAAK,GAAG,EAAE,MAAM,KAAK;AAC9C,QAAI,KAAK;AACP,UAAI,OAAO,WAAW;AACpB,SAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,UAC3C,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA,OACI;AACJ,SAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,OAAO,GAAG;AAAA,MAAA;AAAA,IAQvC,WAAA,iBAAiB,KAAK,EAAG,GAAG;AACrC,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,QAC3C,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA,OACI;AACL,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,IAAA;AAAA,EAO3C,OACK;AACJ,KAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,OAAO,GAAG;AAAA,EAAA;AAQjD,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AASO,MAAM,oBAAoB,CAC/B,OACA,MAGI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;AACtB,MAAA,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IAAA,CACT;AACD,QAAI,WAAW,OAAO;AACb,aAAA;AAAA,QACL,KAAK,MAAM,CAAW;AAAA,QACtB,KAAK,MAAM,CAAW;AAAA,QACtB,KAAK,MAAM,CAAW;AAAA,QACtB;AAAA,MACF;AAAA,IAAA;AAEF,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,QAAQ,MAAM;AACT,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EAOZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAAA,OAOpC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAAA;AAQ3C,GAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC3C,MAAI,WAAW,OAAO;AACpB,WAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,EAAA;AAE5D,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AASO,MAAM,oBAAoB,CAC/B,OACA,MAGI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;AACtB,MAAA,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IAAA,CACT;AAOD,QAAI,WAAW,OAAO;AACpB,aAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,IAAA;AAE5D,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,QAAQ,MAAM;AACT,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EAOZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAAA,OAOpC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAAA;AAQ3C,GAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC3C,MAAI,WAAW,OAAO;AACpB,WAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,EAAA;AAE5D,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AASO,MAAM,oBAAoB,CAC/B,OACA,MAGI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;AACtB,MAAA,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IAAA,CACT;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,WAAW,SAAS;AAClB,QAAA;AACH,QAAgC,MAAM;AACnC,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,QAAQ,MAAM;AACT,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,OAAO;AAAA,MACzC,KAAK;AAAA,IAAA,CACN;AAAA,EAAA,OACI;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,OAAO;AAAA,MAC1C,KAAK;AAAA,IAAA,CACN;AAAA,EAAA;AAEF,GAAA,GAAG,GAAG,CAAC,IAAI,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC9C,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AASO,MAAM,oBAAoB,CAC/B,OACA,MAGI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;AACtB,MAAA,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IAAA,CACT;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,WAAW,SAAS;AAClB,QAAA;AACH,QAAgC,MAAM;AACnC,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,QAAQ,MAAM;AACT,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EAOZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,OAAO;AAAA,MACzC,KAAK;AAAA,IAAA,CACN;AAAA,EAAA,OACI;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,OAAO;AAAA,MAC1C,KAAK;AAAA,IAAA,CACN;AAAA,EAAA;AAEF,GAAA,GAAG,GAAG,CAAC,IAAI,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC9C,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AASO,MAAM,sBAAsB,CACjC,OACA,MAGI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;AACtB,MAAA,UAAU,KAAK,KAAK,GAAG;AACzB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO;AAAA,MACrC,QAAQ;AAAA,IAAA,CACT;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,QAAQ,MAAM;AACT,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAAA,OAOpC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAAA;AAQ3C,GAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC7C,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AASO,MAAM,sBAAsB,CACjC,OACA,MAGI,OACc;AACd,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,MAAI,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;AACtB,MAAA,UAAU,KAAK,KAAK,GAAG;AACzB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO;AAAA,MACrC,QAAQ;AAAA,IAAA,CACT;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,QAAQ,MAAM;AACT,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAAA,OAOpC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAAA;AAQ3C,GAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC7C,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAUO,MAAM,kBAAkB,CAC7B,OACA,MAEI,OACyC;AACzC,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,WAAW;AACnB,QAAM,cAAc,CAAC;AACrB,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,QAAI,MAAM,WAAW,MAAM,KAAK,aAAa,KAAK,KAAK,GAAG;AACxD,YAAM,gBAAgB,IAAI,OAAO,OAAO,MAAM,IAAI,MAAM,IAAI;AACtD,YAAA,QAAQ,MAAM,MAAM,YAAY;AACtC,iBAAW,QAAQ,OAAO;AACpB,YAAA,MAAM,gBAAgB,MAAM;AAAA,UAC9B,QAAQ,WAAW,WAAW,SAAS;AAAA,QAAA,CACxC;AAEG,YAAA,MAAM,QAAQ,GAAG,GAAG;AACtB,gBAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAO7B,cAAI,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,GAAG;AACxC,oBAAA;AACR;AAAA,UAAA;AAEE,cAAA,cAAc,KAAK,EAAE,GAAG;AAC1B,gBAAI,OAAO,GAAG;AACZ,oBAAM,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,YAAA,OAC9B;AACC,oBAAA,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,YAAA;AAAA,UAC7C,WACS,OAAO,GAAG;AACnB,kBAAM,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,UAAA,OACxB;AACC,kBAAA,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,UAAA;AAAA,QAE9B,WAAA,CAAC,QAAQ,KAAK,GAAG,GAAG;AACrB,kBAAA;AACR;AAAA,QAAA;AAEF,oBAAY,KAAK,GAAG;AACZ,gBAAA,MAAM,QAAQ,MAAM,GAAG;AAAA,MAAA;AAEjC,UAAI,CAAC,OAAO;AACV,YAAI,WAAW,UAAU;AAChB,iBAAA;AAAA,QAAA;AAET,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAAA,IAC3B,WACS,WAAW,UAAU;AACvB,aAAA;AAAA,IAAA,OACF;AACL,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EAC3B;AAEF,MAAI,YAAY,QAAQ,QAAQ,MAAM,QAAQ;AAC1C,MAAA,YAAY,UAAU,WAAW,UAAU;AAC7C,UAAM,gBAAgB,IAAI,OAAO,2BAA2B,MAAM,QAAQ;AAC1E,UAAM,CAAG,EAAA,EAAE,IAAI,MAAM,MAAM,aAAa;AACpC,QAAA,WAAW,KAAK,EAAE,GAAG;AACvB,OAAA,EAAG,YAAY,MAAM,IAAI,GAAG,MAAM,UAAU;AAAA,IAAA,OAKvC;AACQ,mBAAA;AAAA,IAAA;AAEX,QAAA,YAAY,WAAW,GAAG;AAC5B,YAAM,QAAQ,YAAY,CAAC,EAAG,QAAQ,aAAa,IAAI;AACvD,YAAM,OAAO,IAAI,OAAO,IAAI,KAAK,YAAY,GAAG,KAAK;AAEnD,OAAA,EAAG,QAAQ,IAAI,IAAI,MAAM,MAAM,IAAI;AAErC,YAAM,QAAQ,YAAY,CAAC,EAAG,QAAQ,aAAa,IAAI;AACvD,YAAM,OAAO,IAAI,OAAO,IAAI,KAAK,YAAY,GAAG,KAAK;AAEnD,OAAA,EAAG,QAAQ,IAAI,IAAI,MAAM,MAAM,IAAI;AAAA,IAAA,OAEhC;AACL,YAAM,YAAY,MAAM,cAAc,WAAW,GAAG;AACpD,YAAM,OAAO,YAAY,CAAC,EAAG,QAAQ,aAAa,IAAI;AACtD,YAAM,WAAW,GAAG,IAAI,UAAU,GAAG;AACrC,YAAM,eAAe,IAAI,IAAI,YAAY,GAAG;AAC5C,YAAM,eAAe,IAAI,OAAO,KAAK,cAAc,YAAY,GAAG,MAAM;AACxE,YAAM,cAAc,IAAI,OAAO,IAAI,YAAY,GAAG;AAClD,YAAM,cAAc,IAAI,OAAO,GAAG,YAAY,UAAU;AAEpD,UAAA,YAAY,KAAK,KAAK,GAAG;AAC3B,cAAM,MAAM,IAAI,OAAO,IAAI,SAAS,cAAc,QAAQ,WAAW;AACrE,cAAM,CAAA,EAAG,YAAY,UAAU,IAAI,MAAM,MAAM,GAAG;AAKlD,SAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,YAAY;AAKhD,SAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,WAAW;AAAA,MAAA,OAK1C;AACL,cAAM,MAAM,IAAI,OAAO,IAAI,QAAQ,cAAc,SAAS,WAAW;AACrE,cAAM,CAAA,EAAG,YAAY,UAAU,IAAI,MAAM,MAAM,GAAG;AAKlD,SAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,WAAW;AAK/C,SAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,YAAY;AAAA,MAAA;AAAA,IAKlD;AAAA,EACF,OACK;AACC,UAAA,CAAA,EAAG,IAAI,YAAY,UAAU,IAAI,MAAM,MAAM,YAAY;AAM/D,UAAM,MAAM,IAAI,OAAO,KAAK,cAAc,YAAY,GAAG,MAAM;AAC/D,KAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,GAAG;AACvC,KAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,GAAG;AACnC,QAAA,WAAW,KAAK,EAAE,GAAG;AACvB,OAAA,EAAG,YAAY,MAAM,IAAI,WAAW,KAAK,EAAE;AAAA,IAAA,OAKtC;AACQ,mBAAA;AAAA,IAAA;AAAA,EACf;AAGF,MAAI,IAAI,IAAI;AACZ,MAAI,QAAQ,MAAM;AACV,UAAA,KAAK,WAAW,IAAI,IAAI;AACxB,UAAA,KAAK,WAAW,IAAI,IAAI;AAC9B,QAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxC,UAAI,WAAW,UAAU;AAChB,eAAA;AAAA,MAAA;AAET,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAE3B,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,GAAG;AAChB,UAAI,WAAW,UAAU;AAChB,eAAA;AAAA,MAAA;AAET,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAE3B,SAAK,KAAK;AACV,SAAK,KAAK;AACN,QAAA,SAAS,IAAI,SAAS;AAAA,EAAA,OACrB;AACL,QAAI,MAAM;AACH,WAAA,WAAW,IAAI,IAAI;AACpB,UAAA,KAAK,KAAK,KAAK,GAAG;AACpB,YAAI,WAAW,UAAU;AAChB,iBAAA;AAAA,QAAA;AAET,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAE3B,WAAK,IAAI;AAAA,eACA,MAAM;AACV,WAAA,WAAW,IAAI,IAAI;AACpB,UAAA,KAAK,KAAK,KAAK,GAAG;AACpB,YAAI,WAAW,UAAU;AAChB,iBAAA;AAAA,QAAA;AAET,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,MAAA;AAE3B,WAAK,IAAI;AAAA,IAAA,OACJ;AACA,WAAA;AACA,WAAA;AAAA,IAAA;AAEH,QAAA;AAAA,EAAA;AAEN,MAAI,eAAe,OAAO;AACX,iBAAA;AAAA,EAAA;AAGf,MAAI,WAAW,UAAU;AACvB,QAAI,QAAQ;AACR,QAAA,OAAO,WAAW,MAAM,GAAG;AACpB,eAAA;AAAA,IACA,WAAA,OAAO,WAAW,QAAQ,GAAG;AAC7B,eAAA,eAAe,QAAQ,GAAG;AAC/B,UAAA,MAAM,QAAQ,MAAM,GAAG;AACnB,cAAA,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM;AACvC,YAAI,OAAO,GAAG;AACZ,mBAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,QAAA,OACjC;AACI,mBAAA,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,QAAA;AAAA,MAChD;AAAA,IACF,OACK;AACI,eAAA,gBAAgB,QAAQ,GAAG;AACpC,UAAI,WAAW,IAAI;AACV,eAAA;AAAA,MAAA;AAEL,UAAA,MAAM,QAAQ,MAAM,GAAG;AACnB,cAAA,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM;AACvC,YAAI,OAAO,GAAG;AACZ,cAAI,OAAO,OAAO;AAChB,qBAAS,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,UAAA,OAC7B;AACL,qBAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,UAAA;AAAA,QAClC,WACS,OAAO,OAAO;AACd,mBAAA,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAAA,OACrC;AACI,mBAAA,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,QAAA;AAAA,MAC1C;AAAA,IACF;AAEE,QAAA,OAAQ,WAAW,MAAM,GAAG;AACrB,eAAA;AAAA,IACA,WAAA,OAAQ,WAAW,QAAQ,GAAG;AAC9B,eAAA,eAAe,QAAS,GAAG;AAChC,UAAA,MAAM,QAAQ,MAAM,GAAG;AACnB,cAAA,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM;AACvC,YAAI,OAAO,GAAG;AACZ,mBAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,QAAA,OACjC;AACI,mBAAA,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,QAAA;AAAA,MAChD;AAAA,IACF,OACK;AACI,eAAA,gBAAgB,QAAS,GAAG;AACrC,UAAI,WAAW,IAAI;AACV,eAAA;AAAA,MAAA;AAEL,UAAA,MAAM,QAAQ,MAAM,GAAG;AACnB,cAAA,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM;AACvC,YAAI,OAAO,GAAG;AACZ,cAAI,OAAO,OAAO;AAChB,qBAAS,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,UAAA,OAC7B;AACL,qBAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,UAAA;AAAA,QAClC,WACS,OAAO,OAAO;AACd,mBAAA,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAAA,OACrC;AACI,mBAAA,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,QAAA;AAAA,MAC1C;AAAA,IACF;AAEF,QAAI,QAAQ,MAAM;AACN,gBAAA,IAAI,WAAW,IAAI,CAAC;AACpB,gBAAA,IAAI,WAAW,IAAI,CAAC;AAAA,eACrB,MAAM;AACTC,YAAAA,MAAK,WAAW,IAAI;AACtBA,UAAAA,QAAO,UAAU,MAAM;AACzB,kBAAU,IAAIA,GAAE;AAAA,MAAA;AAAA,eAET,MAAM;AACTA,YAAAA,MAAK,UAAU,WAAW,IAAI;AAChCA,UAAAA,QAAO,UAAU,MAAM;AACzB,kBAAU,IAAIA,GAAE;AAAA,MAAA;AAAA,IAClB;AAEF,QAAI,QAAQ;AACV,aAAO,gBAAgB,UAAU,IAAI,MAAM,SAAS,MAAM,KAAK,MAAM;AAAA,IAAA,OAChE;AACL,aAAO,gBAAgB,UAAU,KAAK,MAAM,KAAK,MAAM;AAAA,IAAA;AAAA,EACzD;AAEE,MAAA,GAAG,GAAG,GAAG;AAET,MAAA,qBAAqB,KAAK,UAAU,GAAG;AACzC,QAAI,MAAM;AACV,QAAI,eAAe,QAAQ;AACrB,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAS;AAAA,UAChC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH,OACK;AACD,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,wBAAwB,QAAS;AAAA,UACtC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,wBAAwB,QAAS;AAAA,UACtC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH;AAEE,QAAA,SAAS,QAAQ,SAAS,MAAM;AAClC,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAE3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AAM3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AAMrB,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,YAAY,WAAW,QAAQ,WAAW;AAChD,KAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAAA,MAC7C,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB;AAAA,IACF;AACA,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACd;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAErC,QAAI,WAAW,UAAU;AAChB,aAAA;AAAA,QACL;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AAAA,IAAA;AAEG,SAAA;AACA,SAAA;AACA,SAAA;AAAA,EAEI,WAAA,WAAW,KAAK,UAAU,GAAG;AACtC,QAAI,MAAM;AACN,QAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,aAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,IAAA,OACzB;AACL,aAAO,kBAAkB,QAAS;AAAA,QAChC;AAAA,QACA,KAAK,eAAe;AAAA,QACpB,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA;AAEC,QAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,aAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,IAAA,OACzB;AACL,aAAO,kBAAkB,QAAS;AAAA,QAChC;AAAA,QACA,KAAK,eAAe;AAAA,QACpB,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA;AAEC,QAAA,SAAS,QAAQ,SAAS,MAAM;AAClC,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAE3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AAC3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AACrB,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,YAAY,WAAW,QAAQ,WAAW;AAChD,KAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAAA,MAC7C,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB;AAAA,IACF;AACA,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,QAAI,GAAG,GAAG;AACV,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACd;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAErC,QAAI,WAAW,UAAU;AAChB,aAAA;AAAA,QACL;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AAAA,IAAA;AAEF,QAAI,eAAe,WAAW;AAC3B,OAAA,GAAG,GAAG,CAAC,IAAI,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA,OACzC;AACJ,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAGpC,WAAA,eAAe,KAAK,UAAU,GAAG;AAC1C,QAAI,MAAM;AACV,QAAI,eAAe,OAAO;AACpB,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAS;AAAA,UAChC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAS;AAAA,UAChC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH,OACK;AACD,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAS;AAAA,UAChC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAS;AAAA,UAChC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH;AAEE,QAAA,SAAS,QAAQ,SAAS,MAAM;AAClC,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAE3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AAC3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AACrB,UAAA,YAAY,WAAW,QAAQ,WAAW;AAChD,KAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAAA,MAC7C,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,OAAC,IAAI,EAAE,IAAI,eAAe,IAAI,IAAI,MAAM;AAAA,IAAA;AAE1C,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,UAAM,KAAM,KAAgB,KAAM,KAAgB,MAAM;AACxD,QAAI,GAAG;AACP,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACd;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAErC,KAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,GAAG,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AAK7D,QAAI,WAAW,UAAU;AAChB,aAAA;AAAA,QACL;AAAA,QACA,iBAAiB,IAAI,SAAS,GAAG;AAAA,QACjC,iBAAiB,IAAI,SAAS,GAAG;AAAA,QACjC,iBAAiB,IAAI,SAAS,GAAG;AAAA,QACjC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AAAA,IAAA;AAAA,EAGO,WAAA,eAAe,KAAK,UAAU,GAAG;AAC1C,QAAI,MAAM;AACV,QAAI,eAAe,OAAO;AACpB,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAS;AAAA,UAChC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAS;AAAA,UAChC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH,OACK;AACD,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,oBAAoB,QAAS;AAAA,UAClC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,oBAAoB,QAAS;AAAA,UAClC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH;AAEE,QAAA,SAAS,QAAQ,SAAS,MAAM;AAClC,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAE3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AAC3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AACrB,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,YAAY,WAAW,QAAQ,WAAW;AAChD,KAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAAA,MAC7C,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB;AAAA,IACF;AACA,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,QAAI,GAAG,IAAI;AACX,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACd,WAAA,KAAK,KAAK,KAAK;AACf,WAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACf;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC9B,YAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,YAAA,KAAK,UAAU,KAAK,WAAW;AACrC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAErC,QAAI,WAAW,UAAU;AAChB,aAAA;AAAA,QACL;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,IAAI,GAAG;AAAA,QACvC,QAAQ,OAAO,iBAAiB,IAAI,GAAG;AAAA,QACvC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AAAA,IAAA;AAEF,KAAG,EAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,GAAG,UAAU,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG;AAAA,EAOxD,WAAA,eAAe,KAAK,UAAU,GAAG;AAC1C,QAAI,MAAM;AACV,QAAI,eAAe,OAAO;AACpB,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAS;AAAA,UAChC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAS;AAAA,UAChC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH,OACK;AACD,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,oBAAoB,QAAS;AAAA,UAClC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAO,GAAG;AAC7B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,oBAAoB,QAAS;AAAA,UAClC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH;AAEE,QAAA,SAAS,QAAQ,SAAS,MAAM;AAClC,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAE3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AAC3B,QAAI,CAAC,IAAI,IAAI,IAAI,MAAM,IAAI;AACrB,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,QAAQ,OAAO,QAAQ,OAAO;AAC9B,UAAA,YAAY,WAAW,QAAQ,WAAW;AAChD,KAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAAA,MAC7C,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB,CAAC,IAAI,IAAI,IAAI,MAAM;AAAA,MACnB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,OAAC,IAAI,EAAE,IAAI,eAAe,IAAI,IAAI,MAAM;AAAA,IAAA;AAE1C,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,UAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAChC,QAAI,GAAG;AACP,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACd;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAErC,QAAI,WAAW,UAAU;AAChB,aAAA;AAAA,QACL;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AAAA,IAAA;AAEF,KAAG,EAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,GAAG,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AAAA,EAAA;AAO1D,SAAA;AAAA,IACL;AAAA,IACA,KAAK,MAAM,CAAE;AAAA,IACb,KAAK,MAAM,CAAE;AAAA,IACb,KAAK,MAAM,CAAE;AAAA,IACb,YAAY,QAAS,GAAG,QAAQ,CAAC,CAAC;AAAA,EACpC;AACF;"}