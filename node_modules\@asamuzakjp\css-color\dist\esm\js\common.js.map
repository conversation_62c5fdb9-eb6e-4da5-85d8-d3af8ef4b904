{"version": 3, "file": "common.js", "sources": ["../../../src/js/common.ts"], "sourcesContent": ["/**\n * common.js\n */\n\n/* constants */\nconst TYPE_FROM = 8;\nconst TYPE_TO = -1;\n\n/**\n * get type\n * @param {*} o - object to check\n * @returns {string} - type of object\n */\nexport const getType = (o: any): string =>\n  Object.prototype.toString.call(o).slice(TYPE_FROM, TYPE_TO);\n\n/**\n * is string\n * @param {*} o - object to check\n * @returns {boolean} - result\n */\nexport const isString = (o: any): o is string =>\n  typeof o === 'string' || o instanceof String;\n"], "names": [], "mappings": "AAqBO,MAAM,WAAW,CAAC,MACvB,OAAO,MAAM,YAAY,aAAa;"}