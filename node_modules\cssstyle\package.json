{"name": "cssstyle", "description": "CSSStyleDeclaration Object Model implementation", "keywords": ["CSS", "CSSStyleDeclaration", "StyleSheet"], "version": "4.2.1", "homepage": "https://github.com/jsdom/cssstyle", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jon.sakas.co/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://fatfisz.com"}], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/chad3814"}], "repository": "jsdom/cssstyle", "bugs": "https://github.com/jsdom/cssstyle/issues", "directories": {"lib": "./lib"}, "files": ["lib/"], "main": "./lib/CSSStyleDeclaration.js", "dependencies": {"@asamuzakjp/css-color": "^2.8.2", "rrweb-cssom": "^0.8.0"}, "devDependencies": {"@babel/generator": "^7.26.3", "@babel/parser": "^7.26.3", "@babel/traverse": "^7.26.3", "@babel/types": "^7.26.3", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.14.0", "npm-run-all": "^4.1.5", "prettier": "^3.4.2", "resolve": "^1.22.10"}, "scripts": {"download": "node ./scripts/downloadLatestProperties.mjs && eslint lib/allProperties.js --fix", "generate": "run-p generate:*", "generate:implemented_properties": "node ./scripts/generateImplementedProperties.mjs", "generate:properties": "node ./scripts/generate_properties.js", "lint": "npm run generate && eslint --max-warnings 0", "lint:fix": "eslint --fix --max-warnings 0", "prepublishOnly": "npm run lint && npm run test", "test": "npm run generate && node --test", "test-ci": "npm run lint && npm run test", "update-authors": "git log --format=\"%aN <%aE>\" | sort -f | uniq > AUTHORS"}, "license": "MIT", "engines": {"node": ">=18"}}