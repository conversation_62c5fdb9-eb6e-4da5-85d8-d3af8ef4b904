
'use strict';
// autogenerated - 2025-01-09
// https://www.w3.org/Style/CSS/all-properties.en.html

module.exports = new Set([
  "azimuth",
  "background",
  "background-attachment",
  "background-color",
  "background-image",
  "background-position",
  "background-repeat",
  "border",
  "border-bottom",
  "border-bottom-color",
  "border-bottom-style",
  "border-bottom-width",
  "border-collapse",
  "border-color",
  "border-left",
  "border-left-color",
  "border-left-style",
  "border-left-width",
  "border-right",
  "border-right-color",
  "border-right-style",
  "border-right-width",
  "border-spacing",
  "border-style",
  "border-top",
  "border-top-color",
  "border-top-style",
  "border-top-width",
  "border-width",
  "bottom",
  "clear",
  "clip",
  "color",
  "css-float",
  "flex",
  "flex-basis",
  "flex-grow",
  "flex-shrink",
  "float",
  "flood-color",
  "font",
  "font-family",
  "font-size",
  "font-style",
  "font-variant",
  "font-weight",
  "height",
  "left",
  "lighting-color",
  "line-height",
  "margin",
  "margin-bottom",
  "margin-left",
  "margin-right",
  "margin-top",
  "opacity",
  "outline-color",
  "padding",
  "padding-bottom",
  "padding-left",
  "padding-right",
  "padding-top",
  "right",
  "stop-color",
  "text-line-through-color",
  "text-overline-color",
  "text-underline-color",
  "top",
  "webkit-border-after-color",
  "webkit-border-before-color",
  "webkit-border-end-color",
  "webkit-border-start-color",
  "webkit-column-rule-color",
  "webkit-match-nearest-mail-blockquote-color",
  "webkit-tap-highlight-color",
  "webkit-text-emphasis-color",
  "webkit-text-fill-color",
  "webkit-text-stroke-color",
  "width"
]);
